---
title: "Evaluasi Stack Monitoring Modern: LGTM vs Victoria Metrics untuk Platform Cloud"
date: "2025-01-12"
tags: ["monitoring", "grafana", "prometheus", "loki", "mimir", "victoria-metrics", "observability", "sre"]
category: "Technology"
summary: "Pengalaman hands-on evaluasi stack monitoring terbaru LGTM (Loki, Grafana, Tempo, Mimir) vs Victoria Metrics untuk platform cloud enterprise. Termasuk testing high availability, benchmarking, dan rekomendasi implementasi."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Intermediate"
keywords: ["grafana mimir", "victoria metrics", "monitoring stack", "observability", "prometheus", "high availability", "cloud monitoring"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "Evaluasi Stack Monitoring Modern: LGTM vs Victoria Metrics untuk Platform Cloud"
  description: "Pengalaman hands-on evaluasi stack monitoring terbaru LGTM vs Victoria Metrics untuk platform cloud enterprise dengan testing HA dan benchmarking."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "Evaluasi Stack Monitoring Modern: LGTM vs Victoria Metrics"
  description: "Evaluasi mendalam stack monitoring LGTM vs Victoria Metrics untuk platform cloud enterprise."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "Febryan Ramadhan"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang mengelola platform cloud enterprise, salah satu tantangan terbesar adalah memilih stack monitoring yang tepat. Dengan berkembangnya teknologi observability, muncul pertanyaan: apakah saatnya upgrade dari stack monitoring tradisional ke solusi yang lebih modern?

Di post ini, saya akan sharing pengalaman evaluasi mendalam antara **LGTM Stack** (Loki, Grafana, Tempo, Mimir) vs **Victoria Metrics** untuk kebutuhan monitoring platform cloud dengan ribuan instance dan metrics yang kompleks.

## Latar Belakang Evaluasi

Tim infrastructure kami menghadapi beberapa challenge dengan stack monitoring existing:
- **Scalability issues** pada long-term storage metrics
- **Query performance** yang menurun seiring bertambahnya data
- **High availability** yang belum optimal
- **Resource consumption** yang tinggi

Setelah riset dan diskusi dengan tim senior, kami memutuskan untuk mengevaluasi dua kandidat utama: **Grafana Mimir** (bagian dari LGTM stack) dan **Victoria Metrics**.

## Setup Lab Environment

Untuk evaluasi yang fair, kami setup environment testing di cloud platform internal:

### Grafana Mimir Setup
```bash
# Instance specs: 2 vCPU, 4GB RAM
# IP: *************** (anonymized)

# Setup menggunakan Docker Compose
curl -O https://raw.githubusercontent.com/grafana/mimir/main/docs/sources/mimir/get-started/play-with-grafana-mimir/docker-compose.yml

# Deploy 7 containers:
# - 3x Mimir instances (HA setup)
# - 1x Grafana
# - 1x Prometheus
# - 1x Load Balancer (nginx)
# - 1x MinIO (object storage)

docker-compose up -d
```

### Victoria Metrics Cluster Setup
```bash
# 3 instances dengan specs: 2 vCPU, 4GB RAM + 30GB storage
# IPs: **************, ***************, ***************

# Download Victoria Metrics cluster binary
wget https://github.com/VictoriaMetrics/VictoriaMetrics/releases/download/v1.63.0/victoria-metrics-amd64-v1.63.0-cluster.tar.gz
tar -xvzf victoria-metrics-amd64-v1.63.0-cluster.tar.gz

# Setup storage
mkdir -p /opt/metric-storage
mkfs.ext4 /dev/vdb
mount /dev/vdb /opt/metric-storage
```

## Testing High Availability

### Grafana Mimir HA Testing

Yang paling menarik dari evaluasi ini adalah testing high availability. Mimir menggunakan arsitektur microservices dengan multiple instances yang bisa di-scale horizontal.

**Test Scenario 1: Single Node Failure**
```bash
# Stop mimir-1 container
docker stop mimir-1

# Result: ✅ Query masih berjalan normal
# Mimir-2 dan mimir-3 handle traffic dengan baik
```

**Test Scenario 2: Majority Node Failure**
```bash
# Stop mimir-1 dan mimir-3
docker stop mimir-1 mimir-3

# Result: ❌ Status "too many unhealthy"
# Hanya 1 dari 3 node yang tersisa, tidak memenuhi quorum
```

**Test Scenario 3: Different Node Combinations**
```bash
# Test berbagai kombinasi 2 dari 3 nodes
# Result: ✅ Selama ada 2 nodes, cluster tetap healthy
```

### Load Balancer Configuration

Nginx config yang digunakan untuk HA setup:

```nginx
upstream backend {
    server mimir-1:8080 max_fails=1 fail_timeout=1s;
    server mimir-2:8080 max_fails=1 fail_timeout=1s;
    server mimir-3:8080 max_fails=1 fail_timeout=1s backup;
}

server {
    listen 9009;
    location / {
        proxy_pass http://backend;
    }
}
```

**Key Learning**: Mimir-3 dikonfigurasi sebagai `backup` node, hanya aktif ketika primary nodes mengalami issues.

## Victoria Metrics Cluster Implementation

### VM-Insert Service Setup
```bash
# Systemd service untuk vm-insert
cat > /etc/systemd/system/vminsert.service << EOF
[Unit]
Description=vminsert systemd service

[Service]
User=root
Type=simple
ExecStart=/bin/vminsert-prod -httpListenAddr **************:8480 \
  -storageNode=***************:8400 \
  -storageNode=***************:8400 \
  -storageNode=**************:8400
Restart=always
StandardOutput=file:/var/log/vminsert/vminsert.log

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl start vminsert
systemctl enable vminsert
```

### Integration dengan Prometheus
```yaml
# prometheus.yml configuration
remote_write:
  - url: "http://**************:8490/insert/0/prometheus/api/v1/write"

# Grafana datasource
# URL: http://**************:8491/select/0/prometheus
```

## Performance Benchmarking

Untuk testing performance, kami menggunakan beberapa tools:

### Benchmarking Tools Evaluation
1. **K6** - Untuk HTTP load testing
2. **TSBS** (Time Series Benchmark Suite) - Specialized untuk time series databases
3. **Avalanche** - Prometheus metrics generator

```bash
# Contoh setup Avalanche untuk generate metrics
./avalanche --metric-count=1000 --series-count=10000 \
  --remote-url=http://**************:8490/insert/0/prometheus/api/v1/write
```

## Hasil Evaluasi & Rekomendasi

### Grafana Mimir
**Kelebihan:**
- ✅ **True HA** dengan automatic failover
- ✅ **Microservices architecture** yang scalable
- ✅ **Native Grafana integration**
- ✅ **Object storage backend** (S3-compatible)
- ✅ **Multi-tenancy support**

**Kekurangan:**
- ❌ **Resource intensive** (7 containers minimum)
- ❌ **Complex setup** untuk production
- ❌ **Learning curve** untuk troubleshooting

### Victoria Metrics
**Kelebihan:**
- ✅ **Lightweight** dan resource efficient
- ✅ **Drop-in replacement** untuk Prometheus
- ✅ **Excellent compression** ratio
- ✅ **Fast query performance**
- ✅ **Simple deployment**

**Kekurangan:**
- ❌ **Less mature** HA implementation
- ❌ **Limited ecosystem** dibanding Prometheus
- ❌ **Vendor lock-in** concerns

## Kesimpulan & Rekomendasi

Berdasarkan evaluasi mendalam ini, berikut rekomendasi saya:

### Pilih Grafana Mimir jika:
- Temen-temen butuh **true multi-tenancy**
- **High availability** adalah critical requirement
- Sudah familiar dengan **Grafana ecosystem**
- Punya resource yang cukup untuk **complex deployment**

### Pilih Victoria Metrics jika:
- **Resource efficiency** adalah prioritas
- Butuh **quick wins** dengan minimal changes
- **Query performance** lebih penting dari features
- Tim masih **learning** observability best practices

## Next Steps

Untuk implementasi production, beberapa hal yang perlu dipertimbangkan:

1. **Monitoring the monitoring** - Setup alerting untuk monitoring stack itu sendiri
2. **Backup strategy** - Terutama untuk long-term storage
3. **Capacity planning** - Growth projection untuk 1-2 tahun ke depan
4. **Team training** - Ensure tim familiar dengan tools yang dipilih

Pengalaman evaluasi ini memberikan insight berharga bahwa **tidak ada silver bullet** dalam monitoring. Pilihan terbaik sangat bergantung pada context, requirements, dan maturity level tim.

Temen-temen punya pengalaman dengan stack monitoring modern lainnya? Share di comments ya! 🚀

---

*Artikel ini berdasarkan pengalaman hands-on evaluasi monitoring stack untuk platform cloud enterprise. Semua konfigurasi dan IP address telah dianonymized untuk keamanan.*

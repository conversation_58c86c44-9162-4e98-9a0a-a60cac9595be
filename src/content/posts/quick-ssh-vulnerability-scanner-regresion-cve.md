---
title: "Quick SSH Vulnerability Scanner: RegreSSHion CVE-2024-6387 Detection Script"
date: "2025-01-12"
tags: ["security", "ssh", "vulnerability-scanner", "cve", "automation", "quick-fix", "regresion"]
category: "Technology"
summary: "Script sederhana untuk mass scanning SSH vulnerability CVE-2024-6387 (regreSSHion) di infrastructure cloud. Quick detection untuk ratusan instances dengan automated reporting."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Beginner"
keywords: ["ssh vulnerability", "cve scanner", "regresion vulnerability", "automated scanning", "security assessment", "vulnerability detection"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "Quick SSH Vulnerability Scanner: RegreSSHion CVE-2024-6387 Detection Script"
  description: "Script sederhana untuk mass scanning SSH vulnerability CVE-2024-6387 dengan automated reporting untuk infrastructure cloud."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "Quick SSH Vulnerability Scanner: RegreSSHion CVE-2024-6387"
  description: "Mass scanning SSH vulnerability CVE-2024-6387 dengan automated reporting untuk infrastructure cloud."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "Febryan Ramadhan"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang mengelola ratusan instances, ketika **CVE-2024-6387 (regreSSHion)** vulnerability muncul, butuh cara cepat untuk scan semua infrastructure. Manual check satu-satu? Impossible! Butuh **automated scanner** yang bisa handle mass scanning dengan reporting yang clear. 🔍

Di post ini, saya akan sharing **quick SSH vulnerability scanner** untuk detect CVE-2024-6387 yang bisa scan ratusan instances dalam hitungan menit dengan automated CSV reporting.

## CVE-2024-6387: The regreSSHion Threat

### Quick Overview:
```
CVE ID: CVE-2024-6387
Nickname: regreSSHion
Severity: Critical (9.8 CVSS)
Affected: OpenSSH 8.5p1 to 9.8p1
Impact: Remote Code Execution (RCE)
Attack Vector: Unauthenticated remote access
```

### Vulnerable Versions:
- **OpenSSH 8.5p1** hingga **9.8p1**
- **Ubuntu 22.04** dan **RHEL 9** primarily affected
- **Ubuntu 18.04** dan **RHEL 7** generally safe (older SSH versions)

## Quick Detection Script

### Basic SSH Version Checker:
```bash
#!/bin/bash
# check-ssh-cve-6387.sh

check_ssh_vulnerability() {
    local target_ip=$1
    
    echo "Checking SSH vulnerability on $target_ip..."
    
    # Get SSH version
    SSH_VERSION=$(ssh -o ConnectTimeout=5 -o BatchMode=yes $target_ip "ssh -V" 2>&1 | head -1)
    
    if [ $? -ne 0 ]; then
        echo "ERROR: Cannot connect to $target_ip"
        return 1
    fi
    
    # Extract version number
    VERSION_NUM=$(echo "$SSH_VERSION" | grep -oP 'OpenSSH_\K[0-9]+\.[0-9]+p[0-9]+')
    
    # Check vulnerability
    if [[ "$VERSION_NUM" =~ ^([8-9])\.([5-9])p[0-9]+$ ]] || [[ "$VERSION_NUM" =~ ^9\.[0-8]p[0-9]+$ ]]; then
        echo "VULNERABLE: $target_ip - $SSH_VERSION"
        return 2
    else
        echo "SAFE: $target_ip - $SSH_VERSION"
        return 0
    fi
}

# Usage
check_ssh_vulnerability "*************"
```

### Mass Scanner with CSV Output:
```bash
#!/bin/bash
# mass-ssh-vulnerability-scan.sh

SCAN_RESULTS="ssh-vulnerability-scan-$(date +%Y%m%d_%H%M%S).csv"
INSTANCE_LIST="instance-list.txt"

# CSV Header
echo "IP_Address,Hostname,SSH_Version,Vulnerability_Status,OS_Info,Scan_Timestamp" > "$SCAN_RESULTS"

mass_scan_ssh_vulnerability() {
    echo "Starting mass SSH vulnerability scan..."
    echo "Results will be saved to: $SCAN_RESULTS"
    
    while IFS= read -r instance_ip; do
        if [ -n "$instance_ip" ] && [[ ! "$instance_ip" =~ ^# ]]; then
            echo "Scanning: $instance_ip"
            
            # Get hostname
            HOSTNAME=$(ssh -o ConnectTimeout=5 -o BatchMode=yes "$instance_ip" "hostname" 2>/dev/null || echo "unknown")
            
            # Get SSH version
            SSH_VERSION=$(ssh -o ConnectTimeout=5 -o BatchMode=yes "$instance_ip" "ssh -V" 2>&1 | head -1 | tr ',' ' ')
            
            # Get OS info
            OS_INFO=$(ssh -o ConnectTimeout=5 -o BatchMode=yes "$instance_ip" "cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"'" 2>/dev/null || echo "unknown")
            
            # Check vulnerability
            if [ -n "$SSH_VERSION" ]; then
                VERSION_NUM=$(echo "$SSH_VERSION" | grep -oP 'OpenSSH_\K[0-9]+\.[0-9]+p[0-9]+')
                
                if [[ "$VERSION_NUM" =~ ^8\.[5-9]p[0-9]+$ ]] || [[ "$VERSION_NUM" =~ ^9\.[0-8]p[0-9]+$ ]]; then
                    VULN_STATUS="VULNERABLE"
                else
                    VULN_STATUS="SAFE"
                fi
            else
                SSH_VERSION="CONNECTION_FAILED"
                VULN_STATUS="UNKNOWN"
            fi
            
            # Log to CSV
            echo "$instance_ip,$HOSTNAME,$SSH_VERSION,$VULN_STATUS,$OS_INFO,$(date '+%Y-%m-%d %H:%M:%S')" >> "$SCAN_RESULTS"
            
            # Console output
            echo "  Result: $VULN_STATUS - $SSH_VERSION"
        fi
    done < "$INSTANCE_LIST"
    
    echo "Scan completed. Results saved to: $SCAN_RESULTS"
}

# Create sample instance list if not exists
if [ ! -f "$INSTANCE_LIST" ]; then
    cat > "$INSTANCE_LIST" << EOF
# Instance IP addresses to scan
*************
*************
*************
*************
*************
EOF
    echo "Created sample instance list: $INSTANCE_LIST"
    echo "Edit this file with your actual instance IPs"
fi

mass_scan_ssh_vulnerability
```

## OpenStack Integration

### Automated Instance Discovery:
```bash
#!/bin/bash
# openstack-ssh-vulnerability-scan.sh

SITES=("alpha" "beta")
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

scan_openstack_instances() {
    local site=$1
    local output_file="ssh-vuln-scan-$site-$TIMESTAMP.csv"
    
    echo "Scanning OpenStack instances in Site $site..."
    echo "IP_Address,Instance_Name,Project,SSH_Version,Vulnerability_Status,OS_Info" > "$output_file"
    
    # Get all instances with IPs
    openstack --os-cloud "$site" server list --all-projects -f json | jq -r '.[] | select(.Networks != "") | [.Networks, .Name, .Project] | @tsv' | while IFS=$'\t' read -r networks name project; do
        
        # Extract IP address
        INSTANCE_IP=$(echo "$networks" | grep -oP '10\.(10|20)\.\d+\.\d+' | head -1)
        
        if [ -n "$INSTANCE_IP" ]; then
            echo "Scanning: $name ($INSTANCE_IP)"
            
            # SSH version check
            SSH_VERSION=$(timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=no "$INSTANCE_IP" "ssh -V" 2>&1 | head -1 | tr ',' ' ')
            
            # OS info
            OS_INFO=$(timeout 10 ssh -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=no "$INSTANCE_IP" "lsb_release -d 2>/dev/null | cut -f2 || cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"'" 2>/dev/null || echo "unknown")
            
            # Vulnerability assessment
            if [[ "$SSH_VERSION" =~ OpenSSH_([8-9])\.([5-9])p[0-9]+ ]] || [[ "$SSH_VERSION" =~ OpenSSH_9\.[0-8]p[0-9]+ ]]; then
                VULN_STATUS="VULNERABLE"
            elif [[ "$SSH_VERSION" =~ OpenSSH_ ]]; then
                VULN_STATUS="SAFE"
            else
                VULN_STATUS="UNKNOWN"
                SSH_VERSION="CONNECTION_FAILED"
            fi
            
            # Save to CSV
            echo "$INSTANCE_IP,$name,$project,$SSH_VERSION,$VULN_STATUS,$OS_INFO" >> "$output_file"
        fi
    done
    
    echo "Site $site scan completed: $output_file"
}

# Scan all sites
for site in "${SITES[@]}"; do
    scan_openstack_instances "$site" &
done

wait
echo "All scans completed!"
```

## Quick Analysis & Reporting

### Vulnerability Summary Generator:
```bash
#!/bin/bash
# generate-vulnerability-summary.sh

generate_summary() {
    local csv_file=$1
    
    echo "SSH Vulnerability Assessment Summary"
    echo "=================================="
    echo "Report Date: $(date)"
    echo "Source File: $csv_file"
    echo
    
    # Total instances scanned
    TOTAL=$(tail -n +2 "$csv_file" | wc -l)
    echo "Total Instances Scanned: $TOTAL"
    
    # Vulnerability breakdown
    VULNERABLE=$(tail -n +2 "$csv_file" | grep -c "VULNERABLE")
    SAFE=$(tail -n +2 "$csv_file" | grep -c "SAFE")
    UNKNOWN=$(tail -n +2 "$csv_file" | grep -c "UNKNOWN")
    
    echo "Vulnerable Instances: $VULNERABLE"
    echo "Safe Instances: $SAFE"
    echo "Unknown/Failed: $UNKNOWN"
    echo
    
    # Percentage calculation
    if [ $TOTAL -gt 0 ]; then
        VULN_PERCENT=$((VULNERABLE * 100 / TOTAL))
        echo "Vulnerability Rate: $VULN_PERCENT%"
    fi
    
    echo
    echo "Vulnerable Instances Details:"
    echo "----------------------------"
    tail -n +2 "$csv_file" | grep "VULNERABLE" | while IFS=',' read -r ip name project ssh_version status os_info; do
        echo "- $ip ($name) - $ssh_version"
    done
}

# Usage
if [ $# -eq 0 ]; then
    echo "Usage: $0 <csv_file>"
    echo "Example: $0 ssh-vulnerability-scan-20240705_140230.csv"
    exit 1
fi

generate_summary "$1"
```

### One-liner Quick Checks:
```bash
# Quick single instance check
ssh target_ip "ssh -V 2>&1 | grep -q 'OpenSSH_[8-9]\.[5-9]p\|OpenSSH_9\.[0-8]p' && echo 'VULNERABLE' || echo 'SAFE'"

# Quick local check
ssh -V 2>&1 | grep -q 'OpenSSH_[8-9]\.[5-9]p\|OpenSSH_9\.[0-8]p' && echo 'VULNERABLE' || echo 'SAFE'

# Batch check from instance list
while read ip; do echo -n "$ip: "; ssh $ip "ssh -V 2>&1 | grep -q 'OpenSSH_[8-9]\.[5-9]p\|OpenSSH_9\.[0-8]p' && echo 'VULNERABLE' || echo 'SAFE'"; done < instance-list.txt
```

## Mitigation Quick Reference

### Package Updates:
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade openssh-server

# RHEL/CentOS
sudo yum update openssh-server

# Check updated version
ssh -V
```

### Configuration Mitigation (if update not possible):
```bash
# Add to /etc/ssh/sshd_config
echo "LoginGraceTime 0" >> /etc/ssh/sshd_config
systemctl restart sshd
```

## Real-World Usage Example

### Site Alpha Assessment Results:
```
Total Instances Scanned: 45
Vulnerable Instances: 12 (27%)
Safe Instances: 31 (69%)
Unknown/Failed: 2 (4%)

Vulnerable Instances:
- ************* (app-server-01) - OpenSSH_8.9p1
- ************* (database-server) - OpenSSH_9.3p1
- ************* (dev-app-01) - OpenSSH_8.7p1
```

### Site Beta Assessment Results:
```
Total Instances Scanned: 38
Vulnerable Instances: 8 (21%)
Safe Instances: 28 (74%)
Unknown/Failed: 2 (5%)
```

## Automation Integration

### Cron Job for Regular Scanning:
```bash
# Weekly vulnerability scan
0 2 * * 1 /opt/security-scripts/mass-ssh-vulnerability-scan.sh >> /var/log/ssh-vuln-scan.log 2>&1
```

### Slack/Teams Integration:
```bash
# Add to scan script
if [ $VULNERABLE -gt 0 ]; then
    curl -X POST -H 'Content-type: application/json' \
         --data "{\"text\":\"🚨 SSH Vulnerability Alert: $VULNERABLE vulnerable instances found!\"}" \
         "$SLACK_WEBHOOK_URL"
fi
```

## Kesimpulan

Quick SSH vulnerability scanner ini sangat helpful untuk **rapid assessment** CVE-2024-6387 di large-scale infrastructure. Key benefits:

1. **Mass scanning** ratusan instances dalam minutes
2. **Automated reporting** dengan CSV output
3. **OpenStack integration** untuk dynamic instance discovery
4. **Quick analysis** dengan summary generation
5. **Easy automation** dengan cron jobs dan alerting

Yang paling valuable adalah **speed** dan **accuracy** dalam identifying vulnerable instances, enabling rapid response untuk critical security vulnerabilities.

Temen-temen punya vulnerability scanning approaches lainnya? Atau ada improvements untuk script ini? Share di comments ya! 🔒

---

*Script ini berdasarkan pengalaman scanning 200+ instances untuk CVE-2024-6387 assessment. Semua IP addresses telah dianonymized untuk keamanan.*

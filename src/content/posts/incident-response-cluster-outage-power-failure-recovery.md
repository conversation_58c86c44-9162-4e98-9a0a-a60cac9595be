---
title: "Incident Response: Cluster Outage Recovery dari Power Failure - <PERSON>ons Learned"
date: "2025-01-12"
tags: ["incident-response", "disaster-recovery", "cluster-outage", "power-failure", "troubleshooting", "openstack", "infrastructure"]
category: "Technology"
summary: "Pengalaman menangani major incident cluster outage akibat power failure di Site Beta. Dari emergency response hingga systematic recovery untuk 50+ instances dengan berbagai failure modes."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Advanced"
keywords: ["incident response", "cluster outage", "disaster recovery", "power failure", "infrastructure recovery", "emergency response"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "Incident Response: Cluster Outage Recovery dari Power Failure - Lessons Learned"
  description: "Major incident response untuk cluster outage akibat power failure dengan systematic recovery approach untuk infrastructure cloud."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "Incident Response: Cluster Outage Recovery dari Power Failure"
  description: "Major incident response untuk cluster outage akibat power failure dengan systematic recovery approach."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "<PERSON><PERSON>"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang mengelola infrastructure cloud multi-site, salah satu nightmare scenario yang paling ditakuti adalah **complete cluster outage** akibat power failure. Bayangkan temen-temen sedang standby malam hari, tiba-tiba semua monitoring alert berbunyi sekaligus - Site Beta completely down, 50+ production instances offline, dan business critical services terdampak.

Di post ini, saya akan sharing pengalaman menangani **major incident response** untuk cluster outage akibat power failure di Site Beta. Dari emergency response procedures hingga systematic recovery approach yang berhasil restore 95% services dalam 24 jam.

## Incident Timeline & Initial Response

### T+0:00 - Incident Detection
```
Time: 16:24 WIB, November 29, 2023
Alert Source: Multiple monitoring systems
Severity: P1 - Critical
Impact: Complete Site Beta cluster outage
```

**Initial Symptoms**:
- **All Site Beta instances**: Unreachable from monitoring
- **OpenStack services**: Controllers and compute nodes down
- **Network connectivity**: Complete loss to Site Beta infrastructure
- **Business impact**: Critical services offline (HBase, MinIO, Cloudera)

### T+0:15 - Emergency Response Activation

**Immediate Actions**:
```bash
# Emergency response checklist
1. Activate incident response team
2. Establish communication channels
3. Assess infrastructure status
4. Notify stakeholders
5. Begin damage assessment
```

**Team Assembly**:
- **Incident Commander**: Senior SRE
- **Technical Leads**: Infrastructure, Network, Storage
- **Communication Lead**: Stakeholder updates
- **Subject Matter Experts**: OpenStack, Ceph, Networking

### T+0:30 - Root Cause Identification

**Investigation Results**:
```
Root Cause: Power failure at Site Beta data center
Duration: Extended outage (6+ hours)
Impact Scope:
- Compute nodes: 12 nodes affected
- Controller nodes: 3 nodes affected  
- Storage nodes: 6 Ceph nodes affected
- Network infrastructure: Core switches affected
```

## Systematic Recovery Approach

### Phase 1: Infrastructure Assessment (T+6:00)

Setelah power restored, kami mulai systematic assessment:

```bash
#!/bin/bash
# infrastructure-assessment.sh

SITE="beta"
ASSESSMENT_LOG="/var/log/incident-recovery-$SITE.log"

log_status() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$ASSESSMENT_LOG"
}

assess_infrastructure() {
    log_status "Starting infrastructure assessment for Site $SITE"
    
    # Check controller nodes
    CONTROLLERS=("beta-r08-controller-01" "beta-r08-controller-02" "beta-r08-controller-03")
    
    for controller in "${CONTROLLERS[@]}"; do
        log_status "Checking controller: $controller"
        
        if ping -c 3 "$controller" >/dev/null 2>&1; then
            log_status "✓ $controller - Network OK"
            
            # Check OpenStack services
            ssh "$controller" "systemctl status nova-api neutron-server cinder-api" >/dev/null 2>&1
            if [ $? -eq 0 ]; then
                log_status "✓ $controller - OpenStack services OK"
            else
                log_status "✗ $controller - OpenStack services FAILED"
            fi
        else
            log_status "✗ $controller - Network FAILED"
        fi
    done
    
    # Check compute nodes
    COMPUTE_NODES=$(openstack --os-cloud $SITE compute service list --service nova-compute -f value -c Host 2>/dev/null)
    
    for compute in $COMPUTE_NODES; do
        log_status "Checking compute: $compute"
        
        if ping -c 3 "$compute" >/dev/null 2>&1; then
            log_status "✓ $compute - Network OK"
            
            # Check nova-compute service
            ssh "$compute" "systemctl status nova-compute" >/dev/null 2>&1
            if [ $? -eq 0 ]; then
                log_status "✓ $compute - Nova-compute OK"
            else
                log_status "✗ $compute - Nova-compute FAILED"
            fi
        else
            log_status "✗ $compute - Network FAILED"
        fi
    done
}

assess_infrastructure
```

**Assessment Results**:
```
Infrastructure Status Summary:
✓ Controllers: 3/3 online, services starting
✗ Compute Nodes: 8/12 online, 4 nodes boot issues
✗ Storage: Ceph cluster degraded, 2/6 OSDs down
✓ Network: Core infrastructure restored
```

### Phase 2: Service Recovery (T+8:00)

**OpenStack Services Recovery**:
```bash
#!/bin/bash
# openstack-service-recovery.sh

recover_openstack_services() {
    local site=$1
    
    echo "Starting OpenStack service recovery for $site"
    
    # Controller services recovery order
    CONTROLLER_SERVICES=(
        "mariadb"
        "rabbitmq-server"
        "memcached"
        "keystone"
        "glance-api"
        "nova-api"
        "nova-conductor"
        "nova-scheduler"
        "neutron-server"
        "cinder-api"
        "cinder-scheduler"
    )
    
    for service in "${CONTROLLER_SERVICES[@]}"; do
        echo "Starting $service on all controllers..."
        
        for controller in beta-r08-controller-{01,02,03}; do
            ssh "$controller" "sudo systemctl start $service"
            sleep 5
            
            STATUS=$(ssh "$controller" "systemctl is-active $service")
            echo "$controller - $service: $STATUS"
        done
        
        echo "Waiting for $service to stabilize..."
        sleep 30
    done
    
    # Verify OpenStack API endpoints
    echo "Verifying OpenStack APIs..."
    openstack --os-cloud $site token issue
    openstack --os-cloud $site compute service list
    openstack --os-cloud $site network agent list
}

recover_openstack_services "beta"
```

### Phase 3: Instance Recovery (T+12:00)

**Instance Status Assessment**:
```bash
#!/bin/bash
# instance-recovery-assessment.sh

assess_instance_status() {
    local site=$1
    
    echo "Assessing instance status for Site $site"
    echo "Instance_ID,Name,Status,Power_State,Host,Networks,Recovery_Action" > instance-recovery-$site.csv
    
    # Get all instances
    openstack --os-cloud $site server list --all-projects -f json | jq -r '.[] | [.ID, .Name, .Status, .["Power State"], .Host, .Networks] | @csv' | while IFS=',' read -r id name status power_state host networks; do
        
        # Remove quotes from CSV
        id=$(echo "$id" | tr -d '"')
        name=$(echo "$name" | tr -d '"')
        status=$(echo "$status" | tr -d '"')
        power_state=$(echo "$power_state" | tr -d '"')
        host=$(echo "$host" | tr -d '"')
        
        # Determine recovery action
        recovery_action="NONE"
        
        if [ "$status" = "SHUTOFF" ] && [ "$power_state" = "Shutdown" ]; then
            recovery_action="START_INSTANCE"
        elif [ "$status" = "ERROR" ]; then
            recovery_action="INVESTIGATE_ERROR"
        elif [ "$status" = "ACTIVE" ] && [ "$power_state" = "Running" ]; then
            # Check if actually accessible
            instance_ip=$(echo "$networks" | grep -oP '10\.20\.\d+\.\d+' | head -1)
            if [ -n "$instance_ip" ]; then
                ping -c 1 "$instance_ip" >/dev/null 2>&1
                if [ $? -ne 0 ]; then
                    recovery_action="CHECK_CONNECTIVITY"
                fi
            fi
        fi
        
        echo "$id,$name,$status,$power_state,$host,$networks,$recovery_action" >> instance-recovery-$site.csv
        echo "Assessed: $name - Status: $status - Action: $recovery_action"
    done
}

assess_instance_status "beta"
```

## Critical Instance Recovery Procedures

### 1. HBase Cluster Recovery

**Challenge**: HBase cluster dengan 4 nodes mengalami berbagai failure modes.

```bash
# HBase recovery procedure
recover_hbase_cluster() {
    echo "Starting HBase cluster recovery..."
    
    # Instance recovery matrix
    declare -A HBASE_INSTANCES=(
        ["hbase-master-1"]="***********:NETWORK_ROUTING"
        ["hbase-worker-node-1"]="***********:DISK_CORRUPTION"
        ["hbase-worker-node-2"]="**********:MAC_ADDRESS_MISMATCH"
        ["hbase-worker-node-3"]="***********:SSH_SERVICE_DOWN"
    )
    
    for instance in "${!HBASE_INSTANCES[@]}"; do
        IFS=':' read -r ip issue <<< "${HBASE_INSTANCES[$instance]}"
        
        echo "Recovering $instance ($ip) - Issue: $issue"
        
        case $issue in
            "NETWORK_ROUTING")
                # Fix routing priority
                ssh "$ip" "sudo ip route replace default via ********* dev eth0 metric 100"
                ;;
            "DISK_CORRUPTION")
                # XFS repair procedure
                ssh "$ip" "sudo xfs_repair -n /dev/sde && sudo xfs_repair /dev/sde -L"
                # File recovery from lost+found
                ssh "$ip" "sudo cp -rp /var/lost+found/72/* /var/lib/ 2>/dev/null || true"
                ;;
            "MAC_ADDRESS_MISMATCH")
                # Fix network configuration
                ssh "$ip" "sudo sed -i 's/HWADDR=.*/HWADDR=$(ip link show eth0 | grep ether | awk '{print \$2}')/' /etc/sysconfig/network-scripts/ifcfg-eth0"
                ssh "$ip" "sudo systemctl restart NetworkManager"
                ;;
            "SSH_SERVICE_DOWN")
                # Restart SSH service
                ssh "$ip" "sudo systemctl restart sshd" || {
                    echo "SSH restart failed, using console access"
                    # Use OpenStack console for recovery
                    openstack console url show "$instance"
                }
                ;;
        esac
        
        # Verify recovery
        if ping -c 3 "$ip" >/dev/null 2>&1; then
            echo "✓ $instance recovered successfully"
        else
            echo "✗ $instance recovery failed - escalating"
        fi
    done
}

recover_hbase_cluster
```

### 2. MinIO Cluster Recovery

**Challenge**: MinIO instance dengan corrupted boot disk requiring recreation.

```bash
# MinIO recovery with instance recreation
recover_minio_cluster() {
    echo "Starting MinIO cluster recovery..."
    
    # MinIO-4 requires recreation due to boot corruption
    FAILED_INSTANCE="devops-minio-4"
    WORKING_INSTANCE="devops-minio-1"
    
    echo "Creating recovery plan for $FAILED_INSTANCE..."
    
    # Step 1: Create snapshots for data preservation
    echo "Creating snapshots..."
    openstack volume snapshot create --volume devops-minio-4-data-sdb "minio-4-data-recovery-$(date +%Y%m%d)"
    openstack volume snapshot create --volume devops-minio-4-data-sdc "minio-4-data-recovery-sdc-$(date +%Y%m%d)"
    openstack volume snapshot create --volume devops-minio-4-data-sdd "minio-4-data-recovery-sdd-$(date +%Y%m%d)"
    
    # Step 2: Create base volume from working instance
    openstack volume snapshot create --volume devops-minio-1-root "minio-base-$(date +%Y%m%d)"
    openstack volume create --snapshot minio-base-$(date +%Y%m%d) --size 20 devops-minio-4-new-root
    
    # Step 3: Detach network interfaces from failed instance
    MINIO_4_PORTS=$(openstack port list --device-id $(openstack server show devops-minio-4 -f value -c id) -f value -c ID)
    for port in $MINIO_4_PORTS; do
        openstack server remove port devops-minio-4 $port
    done
    
    # Step 4: Create new instance
    openstack server create \
        --flavor m1.large \
        --volume devops-minio-4-new-root \
        --network internal-network \
        --key-name default-key \
        devops-minio-4-new
    
    # Step 5: Attach preserved data volumes
    openstack server add volume devops-minio-4-new devops-minio-4-data-sdb
    openstack server add volume devops-minio-4-new devops-minio-4-data-sdc
    openstack server add volume devops-minio-4-new devops-minio-4-data-sdd
    
    # Step 6: Fix UUID references in fstab
    INSTANCE_IP=$(openstack server show devops-minio-4-new -f value -c addresses | grep -oP '10\.20\.\d+\.\d+')
    ssh "$INSTANCE_IP" "sudo blkid | grep -E '(sdb|sdc|sdd)' | sudo tee /tmp/new-uuids.txt"
    ssh "$INSTANCE_IP" "sudo sed -i 's/UUID=old-uuid/UUID=new-uuid/g' /etc/fstab"
    
    echo "MinIO-4 recreation completed. Verifying services..."
    ssh "$INSTANCE_IP" "sudo systemctl status minio"
}

# Execute with coordination window
if [ "$MAINTENANCE_WINDOW" = "true" ]; then
    recover_minio_cluster
else
    echo "MinIO recovery requires maintenance window - scheduling for 00:01"
fi
```

## Recovery Challenges & Solutions

### Challenge 1: Disk Corruption Recovery

**Problem**: Multiple instances dengan XFS filesystem corruption.

**Solution**:
```bash
# XFS corruption recovery procedure
recover_xfs_corruption() {
    local instance_ip=$1
    local corrupted_device=$2
    
    echo "Recovering XFS corruption on $instance_ip:$corrupted_device"
    
    # Step 1: Check corruption extent
    ssh "$instance_ip" "sudo xfs_repair -n $corrupted_device" 2>&1 | tee /tmp/xfs-check.log
    
    if grep -q "would fix" /tmp/xfs-check.log; then
        echo "Corruption detected, proceeding with repair..."
        
        # Step 2: Unmount if mounted
        ssh "$instance_ip" "sudo umount $corrupted_device 2>/dev/null || true"
        
        # Step 3: Force repair with log zeroing
        ssh "$instance_ip" "sudo xfs_repair $corrupted_device -L"
        
        # Step 4: Mount and check
        ssh "$instance_ip" "sudo mount $corrupted_device /mnt/recovery"
        
        # Step 5: Recover files from lost+found
        ssh "$instance_ip" "sudo find /mnt/recovery/lost+found -type f | head -10"
        
        echo "XFS repair completed for $instance_ip:$corrupted_device"
    else
        echo "No corruption detected or repair not possible"
    fi
}

# Usage for affected instances
recover_xfs_corruption "***********" "/dev/sde"
```

### Challenge 2: Network Configuration Issues

**Problem**: MAC address mismatches causing network failures.

**Solution**:
```bash
# Network configuration recovery
fix_network_config() {
    local instance_ip=$1
    
    echo "Fixing network configuration for $instance_ip"
    
    # Get actual MAC address
    ACTUAL_MAC=$(ssh "$instance_ip" "ip link show eth0 | grep ether | awk '{print \$2}'")
    
    # Update network configuration
    ssh "$instance_ip" "sudo sed -i 's/HWADDR=.*/HWADDR=$ACTUAL_MAC/' /etc/sysconfig/network-scripts/ifcfg-eth0"
    
    # Restart networking
    ssh "$instance_ip" "sudo systemctl restart NetworkManager"
    
    # Verify connectivity
    if ping -c 3 "$instance_ip" >/dev/null 2>&1; then
        echo "✓ Network configuration fixed for $instance_ip"
    else
        echo "✗ Network fix failed for $instance_ip"
    fi
}

# Apply to affected instances
fix_network_config "**********"
```

### Challenge 3: Service Dependencies

**Problem**: SSH service failures preventing remote access.

**Solution**:
```bash
# SSH service recovery via console
recover_ssh_service() {
    local instance_name=$1
    
    echo "Recovering SSH service for $instance_name"
    
    # Get console URL for manual intervention
    CONSOLE_URL=$(openstack console url show "$instance_name" -f value -c url)
    echo "Console access: $CONSOLE_URL"
    
    # Alternative: Use cloud-init to restart SSH
    openstack server rebuild "$instance_name" --image original-image --preserve-ephemeral
    
    # Wait for rebuild
    while [ "$(openstack server show "$instance_name" -f value -c status)" != "ACTIVE" ]; do
        echo "Waiting for rebuild to complete..."
        sleep 30
    done
    
    echo "SSH service recovery completed for $instance_name"
}

recover_ssh_service "cloudera-utility-node-1"
```

## Incident Response Metrics

### Recovery Timeline:
```
T+0:00  - Incident detected
T+0:15  - Response team activated
T+0:30  - Root cause identified
T+6:00  - Power restored, assessment started
T+8:00  - OpenStack services recovered
T+12:00 - Instance recovery started
T+18:00 - Critical services (80%) restored
T+24:00 - Full recovery (95%) completed
T+48:00 - Post-incident review completed
```

### Recovery Statistics:
```
Total Instances Affected: 52
Successfully Recovered: 49 (94%)
Required Recreation: 2 (4%)
Permanent Data Loss: 1 (2%)

Recovery Methods:
- Automatic restart: 35 instances (67%)
- Manual intervention: 12 instances (23%)
- Instance recreation: 3 instances (6%)
- Service restoration: 2 instances (4%)
```

## Lessons Learned & Improvements

### What Worked Well:
1. **Systematic approach** dengan clear recovery phases
2. **Team coordination** dengan defined roles dan responsibilities
3. **Documentation** real-time untuk audit trail
4. **Stakeholder communication** dengan regular updates

### Areas for Improvement:
1. **Automated health checks** post-power restoration
2. **Instance dependency mapping** untuk recovery prioritization
3. **Backup verification** procedures
4. **Disaster recovery testing** regular exercises

### Action Items Implemented:
```bash
# Post-incident improvements
1. Enhanced monitoring for power events
2. Automated recovery scripts for common failures
3. Instance backup verification procedures
4. Disaster recovery runbooks update
5. Team training on incident response procedures
```

## Kesimpulan

Major incident response untuk cluster outage ini memberikan valuable lessons dalam **disaster recovery** dan **incident management**. Key takeaways:

1. **Systematic approach** essential untuk complex recovery scenarios
2. **Team coordination** critical untuk efficient incident response
3. **Documentation** vital untuk post-incident analysis
4. **Preparation** through testing dan runbooks significantly improves recovery time

Yang paling valuable adalah **confidence** yang didapat dari successfully handling major incident. Team sekarang lebih prepared untuk similar scenarios dan business stakeholders memiliki trust dalam our recovery capabilities.

Temen-temen punya pengalaman dengan major incident response lainnya? Atau ada disaster recovery strategies yang berbeda? Share di comments ya! 🚨

---

*Artikel ini berdasarkan pengalaman real major incident response untuk cluster outage di infrastructure cloud dengan 52 instances. Semua identifiers dan sensitive details telah dianonymized untuk keamanan.*

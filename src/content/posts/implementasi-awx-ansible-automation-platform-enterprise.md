---
title: "Implementasi AWX: Ansible Automation Platform untuk Infrastructure Enterprise"
date: "2025-01-12"
tags: ["awx", "ansible", "automation", "kubernetes", "infrastructure-as-code", "devops", "configuration-management"]
category: "Technology"
summary: "Pengalaman hands-on implementasi AWX (Ansible Tower open source) untuk automation platform enterprise. Dari deployment di Kubernetes hingga dynamic inventory dan troubleshooting production issues."
thumbnail: ""
author: "Febryan <PERSON>dha<PERSON>"
difficulty: "Advanced"
keywords: ["awx ansible", "ansible tower", "automation platform", "kubernetes deployment", "infrastructure automation", "configuration management"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "Implementasi AWX: Ansible Automation Platform untuk Infrastructure Enterprise"
  description: "Pengalaman implementasi AWX untuk automation platform enterprise dengan Kubernetes, dynamic inventory, dan production troubleshooting."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "Implementasi AWX: Ansible Automation Platform untuk Infrastructure Enterprise"
  description: "Implementasi AWX untuk automation platform enterprise dengan Kubernetes dan dynamic inventory."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "<PERSON><PERSON>"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang mengelola ratusan instances di infrastructure enterprise, salah satu pain point terbesar adalah **manual configuration management**. Bayangkan temen-temen harus update package security di 200+ servers, atau deploy configuration changes across multiple environments. Manual approach = nightmare! 😱

Di post ini, saya akan sharing pengalaman implementasi **AWX** (open source version dari Ansible Tower) sebagai centralized automation platform. Project ini melibatkan deployment di Kubernetes, setup dynamic inventory, dan real-world troubleshooting untuk production use cases.

## Why AWX for Enterprise Automation?

### Problems We Faced:
- **Manual configuration drift** across environments
- **Inconsistent deployments** dan human errors
- **No audit trail** untuk infrastructure changes
- **Time-consuming** repetitive tasks
- **Lack of standardization** dalam automation practices

### Why AWX Over Alternatives:
- **Open source** alternative to Ansible Tower
- **Web-based UI** untuk non-technical stakeholders
- **Role-based access control** (RBAC)
- **Job scheduling** dan workflow automation
- **Inventory management** dengan dynamic sources
- **Audit logging** untuk compliance

## Architecture Design

Kami design AWX deployment dengan high availability dan scalability considerations:

### Infrastructure Components:
```
┌─────────────────────────────────────────┐
│           Kubernetes Cluster            │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ AWX Control │  │ AWX Executor    │   │
│  │ Plane       │  │ Nodes           │   │
│  │             │  │                 │   │
│  │ - Web UI    │  │ - Job Execution │   │
│  │ - API       │  │ - Ansible       │   │
│  │ - Database  │  │ - Receptor      │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
         │                    │
         └────────┬───────────┘
                  │
    ┌─────────────────────────┐
    │   Target Infrastructure │
    │                         │
    │ ┌─────┐ ┌─────┐ ┌─────┐ │
    │ │Site │ │Site │ │Site │ │
    │ │ GTI │ │ ODC │ │ DEV │ │
    │ └─────┘ └─────┘ └─────┘ │
    └─────────────────────────┘
```

### Deployment Specifications:
- **AWX Control Plane**: 4 vCPU, 8GB RAM
- **AWX Executor**: 2 vCPU, 4GB RAM  
- **Storage**: Local-path provisioner untuk development
- **Network**: Multi-interface untuk site connectivity

## Implementation Journey

### Phase 1: Kubernetes Preparation

Setup Kubernetes cluster dengan requirements untuk AWX:

```bash
# Install local-path storage provisioner
kubectl apply -f https://raw.githubusercontent.com/rancher/local-path-provisioner/v0.0.32/deploy/local-path-storage.yaml

# Set as default storage class
kubectl patch storageclass local-path -p '{"metadata": {"annotations":{"storageclass.kubernetes.io/is-default-class":"true"}}}'

# Verify storage class
kubectl get storageclass
```

### Phase 2: AWX Operator Deployment

Deploy AWX menggunakan operator pattern:

```bash
# Clone AWX operator
git clone https://github.com/ansible/awx-operator.git
cd awx-operator

# Deploy operator
make deploy

# Create AWX instance
cat > awx-instance.yaml << EOF
apiVersion: awx.ansible.com/v1beta1
kind: AWX
metadata:
  name: awx-platform
  namespace: awx
spec:
  service_type: NodePort
  nodeport_port: 30000
  admin_user: admin
  admin_password_secret: awx-admin-password
  postgres_storage_requirements:
    requests:
      storage: 8Gi
  projects_persistence: true
  projects_storage_size: 8Gi
EOF

kubectl apply -f awx-instance.yaml
```

### Phase 3: Network Configuration Challenges

Salah satu challenge terbesar adalah network connectivity antar sites:

**Problem**: AWX perlu akses ke multiple network segments untuk different environments.

**Solution**: Multi-interface configuration dengan netplan:

```yaml
# /etc/netplan/01-netcfg.yaml
network:
  version: 2
  ethernets:
    ens3:
      dhcp4: true
      # Production network interface
    ens4:
      dhcp4: false
      addresses:
        - *************74/24
      # Development network interface
      routes:
        - to: *************/24
          via: *************
```

### Phase 4: Dynamic Inventory Setup

Implementasi dynamic inventory untuk automatic host discovery:

```python
#!/usr/bin/env python3
# openstack_inventory.py
import json
import subprocess
import sys

def get_openstack_instances():
    """Get instances from OpenStack API"""
    try:
        # Execute OpenStack CLI command
        result = subprocess.run([
            'openstack', 'server', 'list', 
            '--format', 'json',
            '--project', 'system-monitoring'
        ], capture_output=True, text=True, check=True)
        
        instances = json.loads(result.stdout)
        
        inventory = {
            '_meta': {
                'hostvars': {}
            },
            'all': {
                'children': ['ungrouped']
            },
            'ungrouped': {
                'hosts': []
            }
        }
        
        for instance in instances:
            if instance['Status'] == 'ACTIVE':
                hostname = instance['Name']
                ip_address = extract_ip(instance['Networks'])
                
                inventory['ungrouped']['hosts'].append(hostname)
                inventory['_meta']['hostvars'][hostname] = {
                    'ansible_host': ip_address,
                    'ansible_user': 'ubuntu',
                    'instance_id': instance['ID'],
                    'instance_status': instance['Status']
                }
        
        return inventory
        
    except subprocess.CalledProcessError as e:
        print(f"Error executing OpenStack command: {e}", file=sys.stderr)
        return {}

def extract_ip(networks_string):
    """Extract IP address from networks string"""
    # Parse networks string format: "network-name=ip1,ip2"
    for network in networks_string.split(';'):
        if '=' in network:
            ips = network.split('=')[1]
            return ips.split(',')[0]  # Return first IP
    return None

if __name__ == '__main__':
    inventory = get_openstack_instances()
    print(json.dumps(inventory, indent=2))
```

### Phase 5: Credential Management

Setup credentials untuk different environments:

```yaml
# AWX Credential Types
- name: "OpenStack Admin"
  credential_type: "OpenStack"
  inputs:
    auth_url: "https://openstack.internal.com:5000/v3"
    username: "admin"
    password: "{{ vault_openstack_password }}"
    project_name: "admin"
    domain_name: "default"

- name: "SSH Key Ubuntu"
  credential_type: "Machine"
  inputs:
    username: "ubuntu"
    ssh_key_data: "{{ vault_ssh_private_key }}"
```

## Real-World Use Cases

### Use Case 1: Security Patch Management

Implementasi automated security patching:

```yaml
---
- name: Security Patch Management
  hosts: all
  remote_user: ubuntu
  gather_facts: yes
  become: yes

  tasks:
    - name: Check sudo version before update
      command: sudo --version
      register: sudo_version_before
      
    - name: Update package cache
      apt:
        update_cache: yes
        cache_valid_time: 3600
        
    - name: Upgrade security packages
      apt:
        upgrade: safe
        autoremove: yes
        autoclean: yes
        
    - name: Check sudo version after update
      command: sudo --version
      register: sudo_version_after
      
    - name: Report version changes
      debug:
        msg: |
          Before: {{ sudo_version_before.stdout_lines[0] }}
          After: {{ sudo_version_after.stdout_lines[0] }}
```

### Use Case 2: Configuration Compliance

Automated compliance checking dan remediation:

```yaml
---
- name: System Compliance Check
  hosts: all
  remote_user: ubuntu
  gather_facts: yes

  tasks:
    - name: Check SSH configuration
      lineinfile:
        path: /etc/ssh/sshd_config
        regexp: '^PermitRootLogin'
        line: 'PermitRootLogin no'
        state: present
      register: ssh_config_changed
      
    - name: Restart SSH if configuration changed
      systemd:
        name: ssh
        state: restarted
      when: ssh_config_changed.changed
      
    - name: Verify firewall status
      ufw:
        state: enabled
        policy: deny
        direction: incoming
```

## Production Challenges & Solutions

### Challenge 1: Container Image Issues

**Problem**: Kubernetes tidak bisa pull required images karena network restrictions.

```bash
# Error yang ditemui
Failed to pull image "redis:latest": rpc error: code = Unknown desc = failed to pull and unpack image
```

**Solution**: Manual image management dan registry mirroring:

```bash
# Pull images manually di local environment
docker pull redis:latest
docker save redis:latest > redis-latest.tar

# Transfer ke production environment
scp redis-latest.tar user@production-server:/tmp/

# Load image di production
sudo ctr -n k8s.io image import /tmp/redis-latest.tar

# Tag dengan expected name
sudo ctr -n k8s.io image tag docker.io/library/redis:latest redis:latest
```

### Challenge 2: Pod Initialization Issues

**Problem**: AWX pods stuck di Init state atau CrashLoopBackOff.

**Troubleshooting Process**:
```bash
# Check pod status
kubectl get pods -n awx

# Describe problematic pods
kubectl describe pod awx-platform-task-xxx -n awx

# Check logs
kubectl logs awx-platform-web-xxx -n awx --previous

# Common issues found:
# 1. Database connection failures
# 2. Storage permission issues
# 3. Network connectivity problems
```

**Solution**: Systematic debugging dan configuration fixes:

```bash
# Fix storage permissions
kubectl exec -it awx-platform-postgres-xxx -n awx -- chown -R postgres:postgres /var/lib/postgresql/data

# Restart deployment
kubectl rollout restart deployment awx-platform-web -n awx
```

### Challenge 3: Multi-Site Connectivity

**Problem**: AWX tidak bisa reach instances di different network segments.

**Solution**: Network routing dan DNS configuration:

```bash
# Configure CoreDNS untuk internal domain resolution
kubectl edit configmap coredns -n kube-system

# Add custom DNS entries
apiVersion: v1
data:
  Corefile: |
    .:53 {
        errors
        health
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
           pods insecure
           fallthrough in-addr.arpa ip6.arpa
           ttl 30
        }
        hosts {
           *************** internal.company.com
           *************** dev.company.com
           fallthrough
        }
        prometheus :9153
        forward . /etc/resolv.conf
        cache 30
        loop
        reload
        loadbalance
    }

# Restart CoreDNS
kubectl rollout restart deployment coredns -n kube-system
```

## Best Practices Learned

### 1. Project Organization
```
AWX Projects Structure:
├── security-patches/
│   ├── ubuntu-security-updates.yml
│   ├── centos-security-updates.yml
│   └── compliance-check.yml
├── application-deployment/
│   ├── web-app-deploy.yml
│   ├── database-setup.yml
│   └── load-balancer-config.yml
└── infrastructure-management/
    ├── user-management.yml
    ├── monitoring-setup.yml
    └── backup-configuration.yml
```

### 2. Credential Management
- **Separate credentials** per environment (dev/staging/prod)
- **Use Ansible Vault** untuk sensitive data
- **Regular rotation** of SSH keys dan passwords
- **Principle of least privilege** untuk access control

### 3. Job Template Design
- **Idempotent playbooks** untuk safe re-execution
- **Proper error handling** dan rollback procedures
- **Comprehensive logging** untuk audit trails
- **Survey variables** untuk dynamic inputs

## Results & Impact

### Quantitative Improvements:
- **Deployment time**: 2 hours → 15 minutes untuk standard configurations
- **Error rate**: 40% reduction dalam configuration errors
- **Audit compliance**: 100% traceability untuk infrastructure changes
- **Team productivity**: 60% time savings untuk repetitive tasks

### Qualitative Benefits:
- **Standardized processes** across all environments
- **Improved collaboration** between teams
- **Better change management** dengan approval workflows
- **Enhanced security** dengan automated compliance checks

## Future Enhancements

### Planned Improvements:
1. **GitOps integration** untuk version-controlled playbooks
2. **Advanced workflows** dengan conditional logic
3. **Custom credential types** untuk specialized systems
4. **Integration** dengan ITSM untuk change management

### Scalability Considerations:
- **Horizontal scaling** dengan multiple executor nodes
- **Database optimization** untuk large inventories
- **Caching strategies** untuk improved performance
- **Load balancing** untuk high availability

## Kesimpulan

Implementasi AWX memberikan significant improvement dalam infrastructure automation maturity. Key takeaways:

1. **Proper planning** essential untuk complex enterprise deployments
2. **Network connectivity** often the biggest challenge dalam multi-site setups
3. **Gradual adoption** lebih efektif daripada big bang approach
4. **Team training** crucial untuk successful adoption

Yang paling valuable adalah **shift from reactive to proactive** infrastructure management. Sekarang kami bisa automate routine tasks dan focus ke strategic improvements.

Temen-temen punya pengalaman dengan automation platforms lainnya? Atau ada challenge khusus dalam implementasi AWX? Share di comments ya! 🚀

---

*Artikel ini berdasarkan pengalaman implementasi AWX untuk infrastructure enterprise dengan 200+ managed nodes. Semua konfigurasi dan credentials telah dianonymized untuk keamanan.*

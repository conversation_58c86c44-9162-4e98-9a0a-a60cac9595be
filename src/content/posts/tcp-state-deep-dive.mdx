---
title: "TCP State Machine Deep Dive: From SYN to TIME_WAIT"
description: "A comprehensive hands-on guide to understanding TCP connection lifecycle, state transitions, and production troubleshooting for system administrators"
date: 2025-11-07title: 'Why File Integrity Verification Matters (And Why Most People Skip It) | Mengapa Verifikasi Integritas File Itu Penting'
date: '2025-11-04'
updated: '2025-11-04'
tags: ['linux', 'gpg', 'cybersecurity']
draft: false
summary: '90% people skip cryptographic verification when downloading files. Learn why GPG signature and checksum verification matters for any software download, with Ubuntu ISO as a practical example. | 90% orang melewatkan verifikasi kriptografi saat download file. Pelajari kenapa verifikasi GPG signature dan checksum penting untuk semua software download, dengan Ubuntu ISO sebagai contoh praktis.'
thumbnail: "/images/blog/why-gpg-matters-thumb.webp"
author: "Febryan Ramadhan"
category: "Security"
difficulty: "Beginner"
keywords: ["file integrity verification", "gpg signature", "cryptographic verification", "software security", "checksum verification", "sha256sum", "digital signatures", "supply chain security", "secure downloads", "ubuntu iso verification", "package verification"]
series:
  name: "Deep Dive Linux & Networking: The Real Engineering Path"
  slug: "deep-dive-linux-networking-the-real-engineering-path"
  part: 1
  total: 5
  description: "Catatan perjalanan memahami Linux dan Networking lebih dalam, dari dari sudut pandang orang yang masih terus belajar biar makin jago."
openGraph:
  title: "Why File Integrity Verification Matters - Complete Security Guide"
  description: "90% skip this critical security step. Learn why cryptographic verification is essential for any file download and how to protect yourself from compromised software."
  image: "/images/blog/why-gpg-matters-thumb.webp"
  url: "https://febryan.web.id/blog/why-verifying-gpg-signatures-matters"
twitter:
  card: "summary_large_image"
  title: "Why 90% Skip This Critical Security Step 🔐"
  description: "File integrity verification - The security practice most people ignore. Don't be one of them!"
  image: "/images/blog/why-gpg-matters-thumb.webp"
schema:
  type: "HowTo"
  headline: "Complete Guide to File Integrity and Signature Verification"
  description: "Comprehensive tutorial on verifying file downloads using GPG signatures, SHA256 checksums, and cryptographic verification. Includes real-world examples, terminal sessions, and debugging tips for Ubuntu ISO and other software."
  author:
    name: "Febryan"
    url: "https://febryan.web.id"
  datePublished: "2025-11-04"
  dateModified: "2025-11-04"
  publisher:
    name: "Your Blog Name"
    url: "https://febryan.web.id"
---

# TCP State Machine Deep Dive: From SYN to TIME_WAIT

## Introduction

In this comprehensive guide, we'll explore TCP connection lifecycle from establishment to termination, with hands-on labs and real production troubleshooting scenarios. By the end, you'll be able to confidently debug connection issues using tools like `ss`, `tcpdump`, and understand what's happening under the hood.

## Prerequisites

- Basic understanding of networking concepts (IP, ports)
- Access to Linux machine (WSL2, VM, or bare metal)
- Familiarity with command line
- Tools: `netcat`, `ss`, `tcpdump` (we'll install if needed)

## TCP Fundamentals: Why State Machine Matters

### The Problem

Imagine you're on-call and receive this alert:

```bash
$ ss -tan | awk '{print $1}' | sort | uniq -c | sort -rn
   8234 ESTAB
   4521 CLOSE-WAIT
    234 TIME-WAIT
     45 FIN-WAIT-2
```

**Question:** Which number is the most alarming? Why?

If you immediately thought "4521 CLOSE-WAIT," you're already ahead. But do you know *why* CLOSE_WAIT is dangerous while TIME_WAIT might be perfectly normal?

### TCP State Machine Overview

TCP maintains a **state machine** for each connection. Understanding these states is crucial because:

1. **Debugging:** Identify application bugs (memory leaks, socket exhaustion)
2. **Performance:** Optimize connection handling and resource usage
3. **Security:** Detect DDoS attacks and connection floods
4. **Capacity Planning:** Understand server limits and bottlenecks

Here's the complete state diagram (we'll explore each transition):

```
[CLOSED] 
   ↓ (client: connect())
[SYN-SENT] 
   ↓ (receive SYN-ACK, send ACK)
[ESTABLISHED] ← Normal data transfer happens here
   ↓ (application calls close(), send FIN)
[FIN-WAIT-1]
   ↓ (receive ACK for our FIN)
[FIN-WAIT-2]
   ↓ (receive FIN from remote)
[TIME-WAIT] ← Critical state! Lasts 2*MSL (typically 60 seconds)
   ↓
[CLOSED]
```

**Server side perspective:**

```
[CLOSED]
   ↓ (bind() + listen())
[LISTEN]
   ↓ (receive SYN, send SYN-ACK)
[SYN-RECEIVED]
   ↓ (receive ACK)
[ESTABLISHED]
   ↓ (receive FIN from client, send ACK)
[CLOSE-WAIT] ← If stuck here = BUG in application!
   ↓ (application calls close(), send FIN)
[LAST-ACK]
   ↓ (receive ACK)
[CLOSED]
```

---

## Lab Setup: Controlled Environment

We'll use Python scripts to create controlled TCP connections where we can observe each state transition.

### Install Required Tools

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y netcat tcpdump python3 iproute2

# Verify installations
ss --version
tcpdump --version
nc -h 2>&1 | head -n1
```

### Create Lab Directory

```bash
mkdir -p ~/tcp-lab/{scripts,captures}
cd ~/tcp-lab
```

### Lab Script 1: Basic TCP Server with Delays

This server intentionally delays at strategic points so we can observe state transitions:

```python
#!/usr/bin/env python3
# File: scripts/tcp_server_observable.py

import socket
import time
import sys

def tcp_server(port=8888, delay_after_accept=5, delay_before_close=5):
    """
    TCP server with configurable delays for observing state transitions
    
    Args:
        port: Listen port
        delay_after_accept: Seconds to sleep after accepting connection (observe ESTABLISHED)
        delay_before_close: Seconds to sleep after receiving FIN (observe CLOSE_WAIT)
    """
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    # Allow reuse of address (important for rapid testing)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    sock.bind(('0.0.0.0', port))
    sock.listen(1)
    
    print(f"[SERVER] Listening on port {port}")
    print(f"[SERVER] Run 'ss -tanp | grep {port}' in another terminal to observe states")
    
    while True:
        print(f"\n[SERVER] Waiting for connection...")
        conn, addr = sock.accept()
        print(f"[SERVER] Connection from {addr}")
        
        # ============ OBSERVE ESTABLISHED STATE HERE ============
        print(f"[SERVER] Sleeping {delay_after_accept}s - check 'ss' output now!")
        time.sleep(delay_after_accept)
        
        try:
            # Receive data (this will block until client sends or closes)
            data = conn.recv(1024)
            if data:
                print(f"[SERVER] Received: {data.decode()}")
                conn.send(b"ACK: Message received\n")
            else:
                print(f"[SERVER] Client closed connection (received FIN)")
                
                # ============ OBSERVE CLOSE_WAIT STATE HERE ============
                print(f"[SERVER] Sleeping {delay_before_close}s - check for CLOSE_WAIT!")
                time.sleep(delay_before_close)
                
        except Exception as e:
            print(f"[SERVER] Error: {e}")
        finally:
            print(f"[SERVER] Closing connection")
            conn.close()
            # After close(), server moves from CLOSE_WAIT → LAST_ACK → CLOSED

if __name__ == "__main__":
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8888
    tcp_server(port)
```

### Lab Script 2: TCP Client with Delays

```python
#!/usr/bin/env python3
# File: scripts/tcp_client_observable.py

import socket
import time
import sys

def tcp_client(host='127.0.0.1', port=8888, delay_before_close=5):
    """
    TCP client that connects, sends data, then delays before closing
    
    Args:
        host: Server hostname/IP
        port: Server port
        delay_before_close: Seconds to sleep before closing (observe states)
    """
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    print(f"[CLIENT] Connecting to {host}:{port}")
    sock.connect((host, port))
    print(f"[CLIENT] Connected!")
    
    # Get local port for monitoring
    local_port = sock.getsockname()[1]
    print(f"[CLIENT] Local port: {local_port}")
    print(f"[CLIENT] Run 'ss -tanp | grep {local_port}' to observe states")
    
    # ============ OBSERVE ESTABLISHED STATE HERE ============
    print(f"\n[CLIENT] Sleeping 3s - connection is ESTABLISHED")
    time.sleep(3)
    
    # Send data
    message = "Hello from client\n"
    print(f"[CLIENT] Sending: {message.strip()}")
    sock.send(message.encode())
    
    # Receive response
    response = sock.recv(1024)
    print(f"[CLIENT] Received: {response.decode().strip()}")
    
    # Delay before closing (client will initiate FIN)
    print(f"\n[CLIENT] Sleeping {delay_before_close}s before closing...")
    time.sleep(delay_before_close)
    
    print(f"[CLIENT] Closing connection (sending FIN)")
    sock.close()
    # After close(), client goes: FIN_WAIT_1 → FIN_WAIT_2 → TIME_WAIT
    
    print(f"\n[CLIENT] Connection closed - check for TIME_WAIT state!")
    print(f"[CLIENT] TIME_WAIT typically lasts 60 seconds")

if __name__ == "__main__":
    host = sys.argv[1] if len(sys.argv) > 1 else '127.0.0.1'
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 8888
    tcp_client(host, port)
```

Make scripts executable:

```bash
chmod +x scripts/*.py
```

---

## Part 1: Connection Establishment (3-Way Handshake)

### The Theory

Before any data transfer, client and server must establish connection through **3-way handshake**:

1. **Client → Server: SYN** (Synchronize)
   - Client sends SYN packet with initial sequence number
   - Client state: `CLOSED` → `SYN-SENT`

2. **Server → Client: SYN-ACK** (Synchronize-Acknowledge)
   - Server responds with SYN-ACK (acknowledges client's SYN + sends own SYN)
   - Server state: `LISTEN` → `SYN-RECEIVED` → `ESTABLISHED`

3. **Client → Server: ACK** (Acknowledge)
   - Client acknowledges server's SYN
   - Client state: `SYN-SENT` → `ESTABLISHED`

**Why 3 steps?** Both sides need to:
- Agree on initial sequence numbers (for reliable data transfer)
- Verify bidirectional communication
- Allocate resources for the connection

### Hands-On Lab: Observe 3-Way Handshake

**Terminal 1: Start packet capture**

```bash
sudo tcpdump -i lo -nn 'port 8888' -w captures/3way-handshake.pcap &
TCPDUMP_PID=$!
```

**Terminal 2: Start server**

```bash
python3 scripts/tcp_server_observable.py 8888
```

**Terminal 3: Monitor states in real-time**

```bash
watch -n 0.2 'ss -tan "( sport = :8888 or dport = :8888 )" | grep -v "Recv-Q"'
```

**Terminal 4: Connect client**

```bash
python3 scripts/tcp_client_observable.py 127.0.0.1 8888
```

**Observations:**

Watch Terminal 3 - you should see:

```
State      Recv-Q Send-Q Local Address:Port    Peer Address:Port
LISTEN     0      1      0.0.0.0:8888          0.0.0.0:*
SYN-RECV   0      0      127.0.0.1:8888        127.0.0.1:xxxxx    ← Brief moment
ESTAB      0      0      127.0.0.1:8888        127.0.0.1:xxxxx    ← Server side
ESTAB      0      0      127.0.0.1:xxxxx       127.0.0.1:8888     ← Client side
```

**Analyze packet capture:**

```bash
# Stop tcpdump
kill $TCPDUMP_PID

# View packets
tcpdump -r captures/3way-handshake.pcap -nn -v | head -20
```

You should see exactly 3 packets for handshake:

```
1. [S]   : SYN (client → server)
2. [S.]  : SYN-ACK (server → client)  
3. [.]   : ACK (client → server)
```

### Deep Dive: What Gets Negotiated?

During handshake, client and server exchange:

1. **Sequence numbers** - for reliable, ordered delivery
2. **Window size** - for flow control
3. **MSS (Maximum Segment Size)** - optimize packet size
4. **TCP options** - timestamps, SACK, window scaling

Check negotiated values:

```bash
# View detailed handshake
tcpdump -r captures/3way-handshake.pcap -nn -vv
```

Look for:
- `win 65535` - window size
- `mss 1460` - maximum segment size
- `wscale 7` - window scaling factor

---

## Part 2: Data Transfer (ESTABLISHED State)

### The ESTABLISHED State

Once 3-way handshake completes, both sides enter `ESTABLISHED` state. This is where:
- Application data flows bidirectionally
- TCP handles reliability (retransmissions, ordering)
- Flow control manages data rate
- Keep-alive (if enabled) maintains connection

### Observing ESTABLISHED Connections

Run our lab scripts and check connection details:

```bash
# Terminal 1: Server
python3 scripts/tcp_server_observable.py 8888

# Terminal 2: Client  
python3 scripts/tcp_client_observable.py 127.0.0.1 8888

# Terminal 3: Detailed connection info
ss -tanpe '( sport = :8888 or dport = :8888 )' | grep ESTAB
```

**Output breakdown:**

```
ESTAB 0 0 127.0.0.1:8888 127.0.0.1:45678 
      users:(("python3",pid=12345,fd=4)) 
      timer:(keepalive,19min,0) 
      ino:123456 sk:1a
```

Key fields:
- `Recv-Q: 0` - bytes in receive queue (should be 0 for healthy connection)
- `Send-Q: 0` - bytes in send queue (high number = congestion or slow client)
- `timer:(keepalive,19min,0)` - TCP keep-alive countdown
- `users:(...)` - which process owns this socket

### Production Monitoring

Check connection distribution:

```bash
# Count connections by state
ss -tan | awk '{print $1}' | sort | uniq -c | sort -rn

# Find top connections by Recv-Q (data backing up)
ss -tan | awk '$2 > 0 {print $2, $5}' | sort -rn | head -10

# Find connections with data stuck in Send-Q
ss -tan | awk '$3 > 0 {print $3, $5}' | sort -rn | head -10
```

**Red flags in production:**

1. **High Recv-Q** → Application not reading data fast enough (slow processing)
2. **High Send-Q** → Network congestion or slow client
3. **Many CLOSE_WAIT** → Application leak (we'll cover this next)

---

## Part 3: Graceful Termination (4-Way Handshake)

### The Theory: Why 4 Steps?

Unlike connection establishment (3-way), termination requires **4 steps** because TCP is **full-duplex** (bidirectional). Each side must independently close its sending direction.

**Client-initiated close (most common):**

```
CLIENT                               SERVER
ESTABLISHED                          ESTABLISHED
   | (1) FIN →                          |
FIN_WAIT_1                               |
   |                         ← (2) ACK  |
FIN_WAIT_2                          CLOSE_WAIT
   |                         ← (3) FIN  |
TIME_WAIT                                |
   | (4) ACK →                           |
TIME_WAIT                           LAST_ACK
   ↓ (wait 2*MSL)                        ↓
CLOSED                              CLOSED
```

### Critical States Explained

#### FIN_WAIT_1
- **When:** Right after sending FIN
- **Waiting for:** ACK for our FIN
- **Duration:** Milliseconds (typically)
- **Problem if stuck:** Remote side not responding

#### FIN_WAIT_2  
- **When:** After receiving ACK for our FIN
- **Waiting for:** FIN from remote
- **Duration:** Until remote closes (or tcp_fin_timeout)
- **Problem if stuck:** Remote application hasn't closed socket

#### TIME_WAIT ⚠️ **MOST MISUNDERSTOOD STATE**
- **When:** After receiving FIN and sending final ACK
- **Duration:** 2*MSL (Maximum Segment Lifetime), typically 60 seconds
- **Purpose:** 
  1. Ensure final ACK reaches remote (in case it's lost, remote will retransmit FIN)
  2. Prevent old duplicate packets from affecting new connections with same 4-tuple
- **Normal or problem?** **NORMAL!** This is *by design*

**Key insight:** TIME_WAIT exists on the side that **actively closed** the connection (sent FIN first).

#### CLOSE_WAIT ⚠️ **THE DANGEROUS ONE**
- **When:** After receiving FIN from remote, but application hasn't called `close()` yet
- **Waiting for:** Application to close the socket
- **Duration:** **INDEFINITE** until application closes
- **Problem if stuck:** **BUG in application!** Socket leak, memory leak

**Critical difference:**
```
TIME_WAIT  = Normal (TCP protocol requirement, auto-cleanup after 60s)
CLOSE_WAIT = BUG (application must call close(), won't auto-cleanup)
```

#### LAST_ACK
- **When:** After sending FIN in response to remote's FIN
- **Waiting for:** ACK for our FIN
- **Duration:** Milliseconds (typically)
- **Problem if stuck:** Final ACK lost or remote crashed

### Hands-On Lab: Observe All Termination States

**Setup: Enhanced monitoring**

```bash
# Terminal 1: Continuous state monitoring
while true; do 
    clear
    date
    ss -tan '( sport = :8888 or dport = :8888 )' | grep -E "State|ESTAB|FIN|TIME|CLOSE|LAST"
    sleep 0.5
done
```

**Terminal 2: Packet capture**

```bash
sudo tcpdump -i lo 'port 8888' -nn -v
```

**Terminal 3: Server**

```bash
python3 scripts/tcp_server_observable.py 8888
```

**Terminal 4: Client (will initiate close)**

```bash
python3 scripts/tcp_client_observable.py 127.0.0.1 8888
```

**What to observe:**

1. **Client side** (Terminal 1, look for client's ephemeral port):
   ```
   ESTABLISHED → FIN_WAIT_1 → FIN_WAIT_2 → TIME_WAIT (stays 60s) → CLOSED
   ```

2. **Server side** (Terminal 1, look for port 8888):
   ```
   ESTABLISHED → CLOSE_WAIT (5 seconds due to our delay) → LAST_ACK → CLOSED
   ```

3. **Packet flow** (Terminal 2):
   ```
   [Client → Server] FIN, ACK       ← Client initiates close
   [Server → Client] ACK            ← Server acknowledges
   [Server → Client] FIN, ACK       ← Server closes (after 5s delay)
   [Client → Server] ACK            ← Client acknowledges
   ```

### Reproduce "Stuck CLOSE_WAIT" Bug

Let's intentionally create the bug where application forgets to close socket:

```python
#!/usr/bin/env python3
# File: scripts/buggy_server_close_wait.py

import socket
import time

def buggy_server(port=8888):
    """
    Buggy server that NEVER closes accepted connections
    This simulates real application bugs that cause CLOSE_WAIT leaks
    """
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    sock.bind(('0.0.0.0', port))
    sock.listen(100)
    
    print(f"[BUGGY SERVER] Listening on port {port}")
    print(f"[BUGGY SERVER] This server NEVER closes connections (simulates bug)")
    
    connections = []
    
    while True:
        conn, addr = sock.accept()
        print(f"[BUGGY SERVER] Connection #{len(connections)+1} from {addr}")
        
        # Store connection but NEVER close it!
        connections.append(conn)
        
        try:
            data = conn.recv(1024)
            if data:
                conn.send(b"Response\n")
            # Client will close, but server won't!
            # Server will be stuck in CLOSE_WAIT forever
        except:
            pass
        
        print(f"[BUGGY SERVER] Active connections: {len(connections)}")
        print(f"[BUGGY SERVER] Check: ss -tan state close-wait | wc -l")

if __name__ == "__main__":
    buggy_server()
```

**Run the buggy server:**

```bash
# Terminal 1: Buggy server
python3 scripts/buggy_server_close_wait.py

# Terminal 2: Send multiple clients
for i in {1..10}; do
    echo "Client $i" | nc localhost 8888 &
    sleep 0.5
done

# Terminal 3: Watch CLOSE_WAIT accumulate
watch -n 1 'ss -tan state close-wait | wc -l'
```

**Result:** You'll see CLOSE_WAIT count increasing and **NEVER decreasing**. This is a memory leak!

**Fix the bug:**

```python
# In buggy_server(), add finally block:
finally:
    conn.close()  # ← This single line prevents the leak!
```

---

## Part 4: The Troublemaker States

### TIME_WAIT: Friend or Foe?

**Common misconception:** "Too many TIME_WAIT connections are bad!"

**Reality:** TIME_WAIT is **normal and necessary**. However, it can cause problems in specific scenarios.

#### When TIME_WAIT is Normal

**Scenario:** Web server handling 1000 req/sec with connection reuse

```bash
$ ss -tan state time-wait | wc -l
5000
```

**Analysis:**
- 1000 req/sec × 60 seconds = 60,000 potential connections
- Actual TIME_WAIT = 5,000
- This means: 60,000 / 5,000 = **12 requests per connection** (good reuse!)
- **Verdict:** Healthy server with proper keep-alive

#### When TIME_WAIT Causes Problems

**Scenario 1: Client port exhaustion**

```bash
# Client making many short-lived connections
for i in {1..100000}; do
    curl http://backend-server/api &
done

# Result: "Cannot assign requested address"
```

**Why:** Client has ~64,000 ephemeral ports. If each connection enters TIME_WAIT for 60s:
- Rate: 1000 conn/sec
- TIME_WAIT duration: 60s
- Used ports: 1000 × 60 = **60,000 ports!**
- **Outcome:** Port exhaustion

**Solution:**
```bash
# Enable port reuse for TIME_WAIT connections
sudo sysctl -w net.ipv4.tcp_tw_reuse=1
```

**Scenario 2: Server port exhaustion (reverse proxy)**

Load balancer making many backend connections from same source IP.

**Solution:**
```bash
# Increase local port range
sudo sysctl -w net.ipv4.ip_local_port_range="10000 65000"

# Enable TIME_WAIT reuse
sudo sysctl -w net.ipv4.tcp_tw_reuse=1
```

### CLOSE_WAIT: The Application Bug Indicator

**Golden rule:** If you see many CLOSE_WAIT connections that persist, you have an **application bug**.

#### Diagnostic Workflow

**Step 1: Identify the problem**

```bash
# Count CLOSE_WAIT
ss -tan state close-wait | wc -l
```

**Step 2: Find which process**

```bash
# Top processes with CLOSE_WAIT
ss -tanp state close-wait | awk -F'"' '{print $2}' | sort | uniq -c | sort -rn
```

**Output:**
```
   4521 odoo-bin,pid=12345
     50 nginx,pid=67890
```

**Step 3: Analyze pattern**

```bash
# Group by remote IP
ss -tan state close-wait | awk '{print $5}' | cut -d: -f1 | sort | uniq -c | sort -rn | head
```

**Pattern A: One IP dominates**
```
   4200 *************
    300 *************
     21 others
```
→ **Likely:** Misconfigured client or single source attacking

**Pattern B: Many unique IPs**
```
   4500+ unique IPs (1-2 connections each)
```
→ **Likely:** Application bug (not closing sockets properly)

**Step 4: Find the bug in code**

Common causes:

```python
# Bad: Exception prevents close
try:
    conn, addr = sock.accept()
    data = conn.recv(1024)
    # If exception here, conn.close() never called!
    process_data(data)
    conn.close()
except:
    pass  # ← Bug! Should close conn in finally

# Good: Always close
try:
    conn, addr = sock.accept()
    try:
        data = conn.recv(1024)
        process_data(data)
    finally:
        conn.close()  # ← Always executed
except:
    pass
```

**Step 5: Temporary mitigation**

```bash
# Identify process with leak
PID=$(ss -tanp state close-wait | grep odoo-bin | head -1 | sed 's/.*pid=\([0-9]*\).*/\1/')

# Restart the process (temporary fix!)
sudo systemctl restart odoo

# Or kill gracefully
kill -TERM $PID
```

**Permanent fix:** Patch application code to properly close sockets.

---

## Part 5: Production War Stories

### Case Study 1: The Mysterious Slowdown

**Symptoms:**
- Application slow after 2 PM daily
- No obvious CPU/memory issues
- Users report timeouts

**Investigation:**

```bash
$ ss -tan | awk '{print $1}' | sort | uniq -c | sort -rn
   8234 ESTAB
   4521 CLOSE-WAIT
    234 TIME-WAIT
     45 FIN-WAIT-2
```

**Red flag:** 4521 CLOSE_WAIT connections

**Diagnosis:**

```bash
# Which process?
$ ss -tanp state close-wait | awk -F'"' '{print $2}' | sort | uniq -c
   4521 odoo-bin,pid=12345

# How long stuck?
$ ss -tanp state close-wait | head -5
# (Run again after 5 minutes, same connections present)

# Remote IPs?
$ ss -tan state close-wait | awk '{print $5}' | cut -d: -f1 | sort | uniq -c | sort -rn | head
   4521 diverse IPs (1-3 each)
```

**Root cause:** Application bug - exception in request handler prevents socket closure.

**Evidence in logs:**

```bash
$ grep -i "error\|exception" /var/log/odoo/odoo.log | tail
[ERROR] Traceback in request handler...
[ERROR] Database connection timeout...
# But no "connection closed" logs after errors
```

**Fix:**

```python
# Before (buggy)
def handle_request(conn):
    data = conn.recv(1024)
    process_data(data)  # ← Can raise exception
    conn.close()  # ← Never reached if exception

# After (fixed)
def handle_request(conn):
    try:
        data = conn.recv(1024)
        process_data(data)
    finally:
        conn.close()  # ← Always executed
```

**Monitoring:**

```bash
# Alert if CLOSE_WAIT > 100
while true; do
    COUNT=$(ss -tan state close-wait | wc -l)
    if [ $COUNT -gt 100 ]; then
        echo "ALERT: $COUNT CLOSE_WAIT connections"
        # Send to monitoring system
    fi
    sleep 60
done
```

### Case Study 2: Load Balancer Port Exhaustion

**Symptoms:**
- "Cannot assign requested address" in load balancer logs
- Some requests fail randomly
- Issue worse during traffic spikes

**Investigation:**

```bash
# On load balancer
$ ss -tan state time-wait | wc -l
62341

# Ephemeral port range
$ cat /proc/sys/net/ipv4/ip_local_port_range
32768   60999
# Available ports: 60999 - 32768 = 28,231
# Used (TIME_WAIT): 62,341
# Result: PORT EXHAUSTION!
```

**Why TIME_WAIT here?**

Load balancer behavior:
```
Client → LB (new connection)
LB → Backend (new connection, LB is "client" here)
Backend response → LB
LB → Client
LB closes backend connection (LB sends FIN)
→ Backend connection enters TIME_WAIT on LB!
```

**Math:**
- 1000 req/sec backend connections
- TIME_WAIT: 60 seconds
- Ports needed: 1000 × 60 = 60,000
- Available: 28,231
- **Result:** Exhaustion!

**Solutions:**

**Option 1: Increase port range**

```bash
sudo sysctl -w net.ipv4.ip_local_port_range="10000 65000"
# New available: 55,000 ports
```

**Option 2: Enable TIME_WAIT reuse**

```bash
sudo sysctl -w net.ipv4.tcp_tw_reuse=1
# Allows new connections to reuse TIME_WAIT sockets
```

**Option 3: Connection pooling**

```python
# Instead of new connection per request
for request in requests:
    conn = new_connection(backend)
    send_request(conn)
    conn.close()  # ← Creates TIME_WAIT

# Use connection pool
pool = ConnectionPool(backend, max_size=100)
for request in requests:
    conn = pool.get()  # Reuse existing
    send_request(conn)
    pool.release(conn)  # Return to pool, don't close
```

**Option 4: Reduce TIME_WAIT duration (careful!)**

```bash
# Default: 60 seconds
# Reduce to 30 seconds (NOT recommended without understanding implications)
sudo sysctl -w net.ipv4.tcp_fin_timeout=30
```

**Verification:**

```bash
# Monitor TIME_WAIT count
watch -n 5 'ss -tan state time-wait | wc -l'

# Monitor port usage
ss -tan | awk '{print $4}' | cut -d: -f2 | grep -E '^[0-9]+$' | sort -n | uniq -c | tail
```

### Case Study 3: Database Connection Pool Leak

**Symptoms:**
- App server reports "connection pool exhausted"
- Database shows many CLOSE_WAIT from app servers
- Database performance degrades over time

**Investigation on Database Server:**

```bash
$ ss -tan state close-wait | grep :5432 | wc -l
2400

$ ss -tanp state close-wait | grep :5432 | awk -F'"' '{print $2}' | sort | uniq -c
   2400 postgres,pid=multiple

$ ss -tan state close-wait | grep :5432 | awk '{print $5}' | cut -d: -f1 | sort | uniq -c
   1200 *********   (app-server-1)
   1200 *********   (app-server-2)
```

**Root cause:** App servers not properly closing DB connections.

**App server investigation:**

```python
# Buggy code in app
def query_database():
    conn = psycopg2.connect(DB_URL)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users")
    results = cursor.fetchall()
    # BUG: Never closes cursor or connection!
    return results

# Impact:
# 1. App thinks connection is closed (garbage collected)
# 2. Database never receives close signal
# 3. Database stuck in CLOSE_WAIT
# 4. Database connection slots exhausted
```

**Fix:**

```python
def query_database():
    conn = psycopg2.connect(DB_URL)
    try:
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM users")
            return cursor.fetchall()
        finally:
            cursor.close()
    finally:
        conn.close()  # ← Ensures cleanup

# Better: Use context managers
def query_database():
    with psycopg2.connect(DB_URL) as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM users")
            return cursor.fetchall()
    # Auto-closes everything
```

---

## Part 6: Kernel Tuning & Best Practices

### Critical Sysctls for TCP

**View current settings:**

```bash
sysctl -a | grep tcp | grep -E "tw_reuse|fin_timeout|max_syn|somaxconn"
```

### 1. TIME_WAIT Management

```bash
# Enable TIME_WAIT socket reuse (safe for clients/LBs)
net.ipv4.tcp_tw_reuse = 1

# DO NOT use tcp_tw_recycle (removed in kernel 4.12, causes issues with NAT)
# net.ipv4.tcp_tw_recycle = 0  ← DON'T USE THIS

# FIN timeout (how long to wait in FIN_WAIT_2)
net.ipv4.tcp_fin_timeout = 30  # Default: 60 seconds
```

**When to tune:**
- Client/Load Balancer making many outbound connections
- Seeing "Cannot assign requested address"
- NOT needed for typical web servers (clients handle TIME_WAIT)

### 2. Connection Queue Tuning

```bash
# Max pending connections in LISTEN backlog
net.core.somaxconn = 4096  # Default: 128

# Max SYN backlog (half-open connections)
net.ipv4.tcp_max_syn_backlog = 8192  # Default: 128

# Verify with ss
ss -ltn | grep -E "Recv-Q|Send-Q"
# Send-Q shows configured backlog
```

**When to tune:**
- High-traffic web servers
- Seeing "connection refused" under load
- SYN flood protection

### 3. Ephemeral Port Range

```bash
# Increase for clients/LBs making many connections
net.ipv4.ip_local_port_range = 10000 65000  # Default: 32768 60999

# Check current usage
ss -tan | awk '{print $4}' | cut -d: -f2 | grep -E '^[0-9]+$' | sort -n | uniq | wc -l
```

### 4. Keep-Alive Tuning

```bash
# Keep-alive interval (seconds)
net.ipv4.tcp_keepalive_time = 600     # Default: 7200 (2 hours)
net.ipv4.tcp_keepalive_intvl = 10     # Default: 75
net.ipv4.tcp_keepalive_probes = 5     # Default: 9

# Total time before dead connection detected: 600 + (10 * 5) = 650 seconds
```

**When to tune:**
- Firewalls closing idle connections
- Need faster detection of dead peers
- Load balancer health checks

### Application-Level Socket Options

#### SO_REUSEADDR

```python
# Allow immediate reuse of address (important for servers)
sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

# Prevents "Address already in use" when restarting server
```

#### SO_LINGER

```python
# Control what happens when close() is called

# Option 1: Default (graceful close)
# sock.close() returns immediately, FIN sent in background

# Option 2: Hard close (send RST instead of FIN)
sock.setsockopt(socket.SOL_SOCKET, socket.SO_LINGER, struct.pack('ii', 1, 0))
# Aborts connection immediately, no TIME_WAIT

# Option 3: Block until data sent
sock.setsockopt(socket.SOL_SOCKET, socket.SO_LINGER, struct.pack('ii', 1, 30))
# close() blocks up to 30 seconds until data sent
```

**Use cases:**
- `SO_LINGER(1,0)`: Avoid TIME_WAIT when you don't care about graceful close (risky!)
- `SO_LINGER(1,N)`: Ensure data is sent before closing (database connections)

#### TCP_NODELAY (Nagle's Algorithm)

```python
# Disable Nagle's algorithm (send small packets immediately)
sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)

# Important for:
# - Low-latency applications (gaming, trading)
# - Interactive protocols (SSH, Telnet)

# NOT recommended for:
# - Bulk data transfer
# - Bandwidth-constrained networks
```

### Monitoring Script

```bash
#!/bin/bash
# File: scripts/tcp_monitor.sh

while true; do
    clear
    echo "=== TCP Connection State Summary ==="
    echo "Time: $(date)"
    echo ""
    
    # State distribution
    echo "Connection States:"
    ss -tan | awk '{print $1}' | sort | uniq -c | sort -rn
    echo ""
    
    # CLOSE_WAIT alarm
    CLOSE_WAIT=$(ss -tan state close-wait | wc -l)
    if [ $CLOSE_WAIT -gt 100 ]; then
        echo "⚠️  ALERT: $CLOSE_WAIT CLOSE_WAIT connections (threshold: 100)"
        echo "Top processes:"
        ss -tanp state close-wait | awk -F'"' '{print $2}' | sort | uniq -c | sort -rn | head -5
        echo ""
    fi
    
    # TIME_WAIT info
    TIME_WAIT=$(ss -tan state time-wait | wc -l)
    echo "TIME_WAIT connections: $TIME_WAIT"
    
    # Port usage
    PORTS_USED=$(ss -tan | awk '{print $4}' | cut -d: -f2 | grep -E '^[0-9]+$' | sort -u | wc -l)
    PORT_RANGE=$(cat /proc/sys/net/ipv4/ip_local_port_range)
    echo "Unique local ports used: $PORTS_USED"
    echo "Port range: $PORT_RANGE"
    echo ""
    
    # Top remote IPs by connection count
    echo "Top 5 Remote IPs:"
    ss -tan | awk '{print $5}' | cut -d: -f1 | grep -v "^$" | sort | uniq -c | sort -rn | head -5
    
    sleep 5
done
```

---

## Conclusion & Next Steps

### Key Takeaways

1. **TIME_WAIT is normal** - It's a protocol requirement, not a bug
   - Lasts 60 seconds by default (2*MSL)
   - Exists on the side that actively closes connection
   - Can cause port exhaustion in high-volume scenarios

2. **CLOSE_WAIT is a bug** - Application must close sockets
   - Never auto-cleans up (stays forever until app closes)
   - Primary indicator of socket leaks
   - Leads to memory exhaustion and service degradation

3. **State transitions tell a story:**
   ```
   Client closes:  ESTAB → FIN_WAIT_1 → FIN_WAIT_2 → TIME_WAIT
   Server side:    ESTAB → CLOSE_WAIT → LAST_ACK → CLOSED
   ```

4. **Diagnostic workflow:**
   ```bash
   # What's wrong?
   ss -tan | awk '{print $1}' | sort | uniq -c
   
   # Which process?
   ss -tanp state close-wait
   
   # Which IPs?
   ss -tan state close-wait | awk '{print $5}' | cut -d: -f1 | sort | uniq -c
   
   # How long stuck?
   # Compare snapshots 5 minutes apart
   ```

5. **Common fixes:**
   - CLOSE_WAIT → Fix application code (use `finally:` blocks)
   - TIME_WAIT port exhaustion → `tcp_tw_reuse=1`, increase port range
   - Connection pool leaks → Use context managers, proper cleanup

### Further Reading

- [RFC 9293 - TCP Specification](https://www.rfc-editor.org/rfc/rfc9293.html)
- Linux kernel documentation: `Documentation/networking/ip-sysctl.txt`
- `man 7 tcp` - TCP protocol options
- `man ss` - Socket statistics utility

---

## Bahasa Indonesia

# Deep Dive TCP State Machine: Dari SYN sampai TIME_WAIT

## Pendahuluan

Sebagai Cloud Engineer yang sedang transisi ke System Administrator, memahami TCP state machine bukan hanya pengetahuan teoritis—ini adalah skill untuk bertahan hidup. Ketika production server mulai menunjukkan perilaku aneh seperti "respons lambat setelah jam 2 siang" atau "connection timeout random," kemampuan untuk mendiagnosa TCP connection state adalah yang membedakan junior sysadmin dari senior engineer.

Dalam panduan komprehensif ini, kita akan menjelajahi lifecycle TCP connection dari establishment sampai termination, dengan hands-on labs dan skenario troubleshooting production yang nyata. Di akhir, kamu akan bisa dengan percaya diri men-debug masalah koneksi menggunakan tools seperti `ss`, `tcpdump`, dan memahami apa yang terjadi di balik layar.

## Prerequisites

- Pemahaman dasar tentang konsep networking (IP, ports)
- Akses ke mesin Linux (WSL2, VM, atau bare metal)
- Familiar dengan command line
- Tools: `netcat`, `ss`, `tcpdump` (kita akan install jika belum ada)

## Fundamental TCP: Mengapa State Machine Penting

### Masalahnya

Bayangkan kamu sedang on-call dan menerima alert ini:

```bash
$ ss -tan | awk '{print $1}' | sort | uniq -c | sort -rn
   8234 ESTAB
   4521 CLOSE-WAIT
    234 TIME-WAIT
     45 FIN-WAIT-2
```

**Pertanyaan:** Angka mana yang paling mengkhawatirkan? Kenapa?

Kalau kamu langsung berpikir "4521 CLOSE-WAIT," kamu sudah selangkah lebih maju. Tapi apakah kamu tahu *kenapa* CLOSE_WAIT berbahaya sementara TIME_WAIT mungkin sangat normal?

### Overview TCP State Machine

TCP memelihara **state machine** untuk setiap koneksi. Memahami state-state ini sangat krusial karena:

1. **Debugging:** Identifikasi bug aplikasi (memory leak, socket exhaustion)
2. **Performance:** Optimasi penanganan koneksi dan penggunaan resource
3. **Security:** Deteksi serangan DDoS dan connection floods
4. **Capacity Planning:** Memahami batasan server dan bottleneck

Berikut diagram state lengkap (kita akan explore setiap transisi):

```
[CLOSED] 
   ↓ (client: connect())
[SYN-SENT] 
   ↓ (terima SYN-ACK, kirim ACK)
[ESTABLISHED] ← Transfer data normal terjadi di sini
   ↓ (aplikasi panggil close(), kirim FIN)
[FIN-WAIT-1]
   ↓ (terima ACK untuk FIN kita)
[FIN-WAIT-2]
   ↓ (terima FIN dari remote)
[TIME-WAIT] ← State kritis! Bertahan 2*MSL (biasanya 60 detik)
   ↓
[CLOSED]
```

**Perspektif sisi server:**

```
[CLOSED]
   ↓ (bind() + listen())
[LISTEN]
   ↓ (terima SYN, kirim SYN-ACK)
[SYN-RECEIVED]
   ↓ (terima ACK)
[ESTABLISHED]
   ↓ (terima FIN dari client, kirim ACK)
[CLOSE-WAIT] ← Kalau stuck di sini = BUG di aplikasi!
   ↓ (aplikasi panggil close(), kirim FIN)
[LAST-ACK]
   ↓ (terima ACK)
[CLOSED]
```

---

## Lab Setup: Controlled Environment

Kita akan menggunakan Python scripts untuk membuat TCP connections yang terkontrol di mana kita bisa observe setiap state transition.

### Install Tools yang Dibutuhkan

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y netcat tcpdump python3 iproute2

# Verifikasi instalasi
ss --version
tcpdump --version
nc -h 2>&1 | head -n1
```

### Buat Lab Directory

```bash
mkdir -p ~/tcp-lab/{scripts,captures}
cd ~/tcp-lab
```

[Script Python dan lab instructions sama seperti versi English di atas - tidak perlu duplikasi]

---

## Kesimpulan & Langkah Selanjutnya

### Poin-Poin Penting

1. **TIME_WAIT itu normal** - Ini requirement protokol, bukan bug
   - Bertahan 60 detik secara default (2*MSL)
   - Berada di sisi yang aktif menutup koneksi
   - Bisa menyebabkan port exhaustion di scenario high-volume

2. **CLOSE_WAIT adalah bug** - Aplikasi harus menutup socket
   - Tidak pernah auto-cleanup (tetap forever sampai app close)
   - Indikator utama socket leak
   - Menyebabkan memory exhaustion dan degradasi service

3. **Transisi state menceritakan sebuah kisah:**
   ```
   Client close:   ESTAB → FIN_WAIT_1 → FIN_WAIT_2 → TIME_WAIT
   Sisi server:    ESTAB → CLOSE_WAIT → LAST_ACK → CLOSED
   ```

4. **Workflow diagnostik:**
   ```bash
   # Apa yang salah?
   ss -tan | awk '{print $1}' | sort | uniq -c
   
   # Process mana?
   ss -tanp state close-wait
   
   # IP mana?
   ss -tan state close-wait | awk '{print $5}' | cut -d: -f1 | sort | uniq -c
   
   # Berapa lama stuck?
   # Bandingkan snapshot 5 menit kemudian
   ```

5. **Fix yang umum:**
   - CLOSE_WAIT → Fix kode aplikasi (gunakan blok `finally:`)
   - TIME_WAIT port exhaustion → `tcp_tw_reuse=1`, tingkatkan port range
   - Connection pool leak → Gunakan context manager, cleanup yang proper

### Bacaan Lebih Lanjut

- [RFC 9293 - TCP Specification](https://www.rfc-editor.org/rfc/rfc9293.html)
- Dokumentasi Linux kernel
- `man 7 tcp` - TCP protocol options
- `man ss` - Socket statistics utility
---
---
title: "FIO Storage Benchmarking: Enterprise Database Performance Testing untuk OpenStack Infrastructure"
date: "2025-01-12"
tags: ["fio-benchmarking", "storage-performance", "database-testing", "openstack-performance", "enterprise-db", "performance-analysis"]
category: "Technology"
summary: "Comprehensive FIO storage benchmarking untuk enterprise database requirements di OpenStack infrastructure. Dari methodology hingga analysis untuk performance validation."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Advanced"
keywords: ["fio benchmarking", "storage performance testing", "database performance", "openstack storage", "enterprise database", "performance validation"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "FIO Storage Benchmarking: Enterprise Database Performance Testing untuk OpenStack Infrastructure"
  description: "Comprehensive FIO storage benchmarking methodology untuk enterprise database performance validation di OpenStack."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "FIO Storage Benchmarking: Enterprise Database Performance Testing"
  description: "FIO storage benchmarking methodology untuk enterprise database performance validation."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "<PERSON><PERSON>"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang mengelola **OpenStack infrastructure** untuk enterprise workloads, salah satu challenge paling critical adalah **storage performance validation** untuk database applications. Ketika DBA team membutuhkan **performance benchmarking data** untuk evaluate apakah OpenStack storage memenuhi Enterprise DB requirements, butuh comprehensive testing methodology. 📊

Di post ini, saya akan sharing pengalaman **FIO storage benchmarking** untuk enterprise database performance testing dengan systematic approach dari planning hingga analysis untuk production validation.

## Benchmarking Requirements & Objectives

### Enterprise Database Performance Requirements:
```
Target Application: Enterprise Database (EDB)
Performance Requirements:
- Random Read IOPS: >10,000 IOPS
- Random Write IOPS: >5,000 IOPS
- Sequential Read: >500 MB/s
- Sequential Write: >300 MB/s
- Latency: <5ms (95th percentile)
- Consistency: <10% variance across tests

Test Environment:
- Instance Specs: 24 vCPU, 32GB RAM
- Storage Types: Dedicated vs Shared
- Sites: Site Alpha & Site Beta
- Duration: 7 days comprehensive testing
```

### Benchmarking Methodology Framework:
```bash
#!/bin/bash
# fio-benchmarking-framework.sh

setup_benchmarking_environment() {
    echo "FIO Benchmarking Framework Setup"
    echo "==============================="
    echo "Setup Date: $(date)"
    
    # Install FIO and dependencies
    echo "1. Installing FIO and dependencies..."
    apt update
    apt install -y fio sysstat iotop htop nvme-cli
    echo "   ✓ FIO and monitoring tools installed"
    
    # Create test directory structure
    echo
    echo "2. Creating test directory structure..."
    mkdir -p /benchmark/{results,configs,logs,scripts}
    mkdir -p /benchmark/testfiles
    echo "   ✓ Directory structure created"
    
    # System information collection
    echo
    echo "3. Collecting system information..."
    
    # Hardware information
    lscpu > /benchmark/results/system-cpu-info.txt
    free -h > /benchmark/results/system-memory-info.txt
    lsblk > /benchmark/results/system-storage-info.txt
    
    # Storage device details
    for device in $(lsblk -d -o NAME | grep -v NAME); do
        if [[ "$device" =~ ^(sd|nvme|vd) ]]; then
            echo "Device: /dev/$device" >> /benchmark/results/storage-details.txt
            fdisk -l /dev/$device >> /benchmark/results/storage-details.txt 2>/dev/null
            echo "---" >> /benchmark/results/storage-details.txt
        fi
    done
    
    echo "   ✓ System information collected"
    
    # Network and instance metadata
    echo
    echo "4. Collecting instance metadata..."
    curl -s http://***************/latest/meta-data/instance-type > /benchmark/results/instance-type.txt 2>/dev/null || echo "unknown" > /benchmark/results/instance-type.txt
    hostname > /benchmark/results/hostname.txt
    echo "   ✓ Instance metadata collected"
}

setup_benchmarking_environment
```

## Comprehensive FIO Test Suite

### Database-Optimized Test Configurations:
```bash
#!/bin/bash
# create-fio-test-configs.sh

create_database_optimized_configs() {
    echo "Creating Database-Optimized FIO Configurations"
    echo "=============================================="
    
    # 1. Random Read Test (Database Query Simulation)
    cat > /benchmark/configs/random-read-test.fio << 'EOF'
[global]
ioengine=libaio
direct=1
size=10G
runtime=300
time_based=1
group_reporting=1
filename=/benchmark/testfiles/random-read-test

[random-read-4k]
name=random-read-4k
rw=randread
bs=4k
iodepth=32
numjobs=4

[random-read-8k]
name=random-read-8k
rw=randread
bs=8k
iodepth=16
numjobs=2

[random-read-16k]
name=random-read-16k
rw=randread
bs=16k
iodepth=8
numjobs=2
EOF
    
    # 2. Random Write Test (Database Transaction Simulation)
    cat > /benchmark/configs/random-write-test.fio << 'EOF'
[global]
ioengine=libaio
direct=1
size=10G
runtime=300
time_based=1
group_reporting=1
filename=/benchmark/testfiles/random-write-test

[random-write-4k]
name=random-write-4k
rw=randwrite
bs=4k
iodepth=16
numjobs=2

[random-write-8k]
name=random-write-8k
rw=randwrite
bs=8k
iodepth=8
numjobs=2

[random-write-16k]
name=random-write-16k
rw=randwrite
bs=16k
iodepth=4
numjobs=1
EOF
    
    # 3. Mixed Workload Test (Real Database Simulation)
    cat > /benchmark/configs/mixed-workload-test.fio << 'EOF'
[global]
ioengine=libaio
direct=1
size=10G
runtime=600
time_based=1
group_reporting=1
filename=/benchmark/testfiles/mixed-workload-test

[mixed-workload-70-30]
name=mixed-workload-70-30
rw=randrw
rwmixread=70
bs=4k
iodepth=16
numjobs=4

[mixed-workload-80-20]
name=mixed-workload-80-20
rw=randrw
rwmixread=80
bs=8k
iodepth=8
numjobs=2
EOF
    
    # 4. Sequential Read Test (Backup/Recovery Simulation)
    cat > /benchmark/configs/sequential-read-test.fio << 'EOF'
[global]
ioengine=libaio
direct=1
size=20G
runtime=300
time_based=1
group_reporting=1
filename=/benchmark/testfiles/sequential-read-test

[sequential-read-64k]
name=sequential-read-64k
rw=read
bs=64k
iodepth=32
numjobs=4

[sequential-read-1m]
name=sequential-read-1m
rw=read
bs=1m
iodepth=8
numjobs=2
EOF
    
    # 5. Sequential Write Test (Log Writing Simulation)
    cat > /benchmark/configs/sequential-write-test.fio << 'EOF'
[global]
ioengine=libaio
direct=1
size=20G
runtime=300
time_based=1
group_reporting=1
filename=/benchmark/testfiles/sequential-write-test

[sequential-write-64k]
name=sequential-write-64k
rw=write
bs=64k
iodepth=16
numjobs=2

[sequential-write-1m]
name=sequential-write-1m
rw=write
bs=1m
iodepth=4
numjobs=1
EOF
    
    echo "✓ Database-optimized FIO configurations created"
}

create_database_optimized_configs
```

### Automated Test Execution Framework:
```bash
#!/bin/bash
# execute-comprehensive-benchmark.sh

execute_comprehensive_benchmark() {
    echo "Executing Comprehensive Storage Benchmark"
    echo "========================================="
    echo "Start Time: $(date)"
    
    # Test configurations
    TESTS=("random-read-test" "random-write-test" "mixed-workload-test" "sequential-read-test" "sequential-write-test")
    INSTANCE_TYPE=$(cat /benchmark/results/instance-type.txt)
    HOSTNAME=$(cat /benchmark/results/hostname.txt)
    
    # Create results directory for this run
    RUN_ID="benchmark-$(date +%Y%m%d_%H%M%S)"
    RESULTS_DIR="/benchmark/results/$RUN_ID"
    mkdir -p "$RESULTS_DIR"
    
    echo "Benchmark Run ID: $RUN_ID"
    echo "Instance: $HOSTNAME ($INSTANCE_TYPE)"
    echo
    
    # Pre-test system state
    echo "1. Capturing pre-test system state..."
    iostat -x 1 5 > "$RESULTS_DIR/pre-test-iostat.txt" &
    IOSTAT_PID=$!
    sleep 6
    kill $IOSTAT_PID 2>/dev/null
    
    free -h > "$RESULTS_DIR/pre-test-memory.txt"
    df -h > "$RESULTS_DIR/pre-test-disk.txt"
    echo "   ✓ Pre-test state captured"
    
    # Execute each test
    for test in "${TESTS[@]}"; do
        echo
        echo "2. Executing test: $test"
        echo "   Start time: $(date)"
        
        # Clear caches before each test
        sync
        echo 3 > /proc/sys/vm/drop_caches
        
        # Start system monitoring
        iostat -x 1 > "$RESULTS_DIR/${test}-iostat.txt" &
        IOSTAT_PID=$!
        
        sar -u 1 > "$RESULTS_DIR/${test}-cpu.txt" &
        SAR_PID=$!
        
        # Execute FIO test
        fio /benchmark/configs/${test}.fio \
            --output="$RESULTS_DIR/${test}-results.txt" \
            --output-format=normal,json \
            --write_lat_log="$RESULTS_DIR/${test}-latency" \
            --write_iops_log="$RESULTS_DIR/${test}-iops" \
            --write_bw_log="$RESULTS_DIR/${test}-bandwidth"
        
        # Stop monitoring
        kill $IOSTAT_PID $SAR_PID 2>/dev/null
        
        echo "   ✓ Test completed: $test"
        echo "   End time: $(date)"
        
        # Brief pause between tests
        sleep 30
    done
    
    # Post-test system state
    echo
    echo "3. Capturing post-test system state..."
    iostat -x 1 5 > "$RESULTS_DIR/post-test-iostat.txt" &
    IOSTAT_PID=$!
    sleep 6
    kill $IOSTAT_PID 2>/dev/null
    
    free -h > "$RESULTS_DIR/post-test-memory.txt"
    df -h > "$RESULTS_DIR/post-test-disk.txt"
    echo "   ✓ Post-test state captured"
    
    # Generate summary report
    echo
    echo "4. Generating summary report..."
    generate_summary_report "$RESULTS_DIR"
    echo "   ✓ Summary report generated"
    
    echo
    echo "Benchmark completed successfully!"
    echo "Results directory: $RESULTS_DIR"
    echo "End Time: $(date)"
}

generate_summary_report() {
    local results_dir=$1
    local summary_file="$results_dir/benchmark-summary.txt"
    
    echo "Storage Performance Benchmark Summary" > "$summary_file"
    echo "====================================" >> "$summary_file"
    echo "Generated: $(date)" >> "$summary_file"
    echo "Instance: $(cat /benchmark/results/hostname.txt)" >> "$summary_file"
    echo "Instance Type: $(cat /benchmark/results/instance-type.txt)" >> "$summary_file"
    echo >> "$summary_file"
    
    # Extract key metrics from each test
    for test in random-read-test random-write-test mixed-workload-test sequential-read-test sequential-write-test; do
        if [ -f "$results_dir/${test}-results.txt" ]; then
            echo "Test: $test" >> "$summary_file"
            echo "$(grep -A 10 "read:" "$results_dir/${test}-results.txt" | head -5)" >> "$summary_file"
            echo "$(grep -A 10 "write:" "$results_dir/${test}-results.txt" | head -5)" >> "$summary_file"
            echo >> "$summary_file"
        fi
    done
}

execute_comprehensive_benchmark
```

## Performance Analysis & Comparison

### Dedicated vs Shared Storage Analysis:
```bash
#!/bin/bash
# analyze-storage-performance.sh

analyze_storage_performance() {
    echo "Storage Performance Analysis"
    echo "==========================="
    
    # Define test instances
    DEDICATED_INSTANCES=("alpha-db-dedicated-01:10.10.216.100" "beta-db-dedicated-01:10.20.216.100")
    SHARED_INSTANCES=("alpha-db-shared-01:*************" "beta-db-shared-01:*************")
    
    echo "1. Collecting performance data from all instances..."
    
    # Create comparison directory
    COMPARISON_DIR="/benchmark/comparison-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$COMPARISON_DIR"/{dedicated,shared}
    
    # Collect data from dedicated instances
    echo "   Collecting from dedicated instances..."
    for instance_info in "${DEDICATED_INSTANCES[@]}"; do
        IFS=':' read -r instance_name instance_ip <<< "$instance_info"
        echo "     Processing: $instance_name"
        
        # Copy latest benchmark results
        scp -r "$instance_ip:/benchmark/results/benchmark-*" "$COMPARISON_DIR/dedicated/$instance_name/" 2>/dev/null || echo "     No results found for $instance_name"
    done
    
    # Collect data from shared instances
    echo "   Collecting from shared instances..."
    for instance_info in "${SHARED_INSTANCES[@]}"; do
        IFS=':' read -r instance_name instance_ip <<< "$instance_info"
        echo "     Processing: $instance_name"
        
        # Copy latest benchmark results
        scp -r "$instance_ip:/benchmark/results/benchmark-*" "$COMPARISON_DIR/shared/$instance_name/" 2>/dev/null || echo "     No results found for $instance_name"
    done
    
    echo "   ✓ Performance data collected"
    
    # Generate comparative analysis
    echo
    echo "2. Generating comparative analysis..."
    generate_comparative_analysis "$COMPARISON_DIR"
    echo "   ✓ Comparative analysis completed"
}

generate_comparative_analysis() {
    local comparison_dir=$1
    local analysis_file="$comparison_dir/performance-comparison.txt"
    
    echo "Storage Performance Comparison Analysis" > "$analysis_file"
    echo "=======================================" >> "$analysis_file"
    echo "Generated: $(date)" >> "$analysis_file"
    echo >> "$analysis_file"
    
    # Analyze dedicated storage performance
    echo "DEDICATED STORAGE PERFORMANCE:" >> "$analysis_file"
    echo "-----------------------------" >> "$analysis_file"
    
    for dedicated_dir in "$comparison_dir/dedicated"/*; do
        if [ -d "$dedicated_dir" ]; then
            instance_name=$(basename "$dedicated_dir")
            echo "Instance: $instance_name" >> "$analysis_file"
            
            # Extract key metrics if available
            latest_result=$(find "$dedicated_dir" -name "benchmark-*" -type d | sort | tail -1)
            if [ -n "$latest_result" ]; then
                echo "  Random Read IOPS: $(grep -h "read:" "$latest_result"/random-read-test-results.txt | grep -o "[0-9]*k" | head -1)" >> "$analysis_file" 2>/dev/null
                echo "  Random Write IOPS: $(grep -h "write:" "$latest_result"/random-write-test-results.txt | grep -o "[0-9]*k" | head -1)" >> "$analysis_file" 2>/dev/null
                echo "  Sequential Read BW: $(grep -h "read:" "$latest_result"/sequential-read-test-results.txt | grep -o "[0-9]*MB/s" | head -1)" >> "$analysis_file" 2>/dev/null
            fi
            echo >> "$analysis_file"
        fi
    done
    
    # Analyze shared storage performance
    echo "SHARED STORAGE PERFORMANCE:" >> "$analysis_file"
    echo "---------------------------" >> "$analysis_file"
    
    for shared_dir in "$comparison_dir/shared"/*; do
        if [ -d "$shared_dir" ]; then
            instance_name=$(basename "$shared_dir")
            echo "Instance: $instance_name" >> "$analysis_file"
            
            # Extract key metrics if available
            latest_result=$(find "$shared_dir" -name "benchmark-*" -type d | sort | tail -1)
            if [ -n "$latest_result" ]; then
                echo "  Random Read IOPS: $(grep -h "read:" "$latest_result"/random-read-test-results.txt | grep -o "[0-9]*k" | head -1)" >> "$analysis_file" 2>/dev/null
                echo "  Random Write IOPS: $(grep -h "write:" "$latest_result"/random-write-test-results.txt | grep -o "[0-9]*k" | head -1)" >> "$analysis_file" 2>/dev/null
                echo "  Sequential Read BW: $(grep -h "read:" "$latest_result"/sequential-read-test-results.txt | grep -o "[0-9]*MB/s" | head -1)" >> "$analysis_file" 2>/dev/null
            fi
            echo >> "$analysis_file"
        fi
    done
    
    # Performance recommendations
    echo "PERFORMANCE RECOMMENDATIONS:" >> "$analysis_file"
    echo "----------------------------" >> "$analysis_file"
    echo "1. Dedicated storage shows consistently higher IOPS performance" >> "$analysis_file"
    echo "2. Shared storage provides adequate performance for most workloads" >> "$analysis_file"
    echo "3. Consider dedicated storage for high-performance database workloads" >> "$analysis_file"
    echo "4. Shared storage suitable for development and testing environments" >> "$analysis_file"
}

analyze_storage_performance
```

## Enterprise Database Validation Results

### Performance Benchmark Results Summary:
```
FIO Storage Benchmark Results Summary
====================================

Test Environment:
- Instance Specs: 24 vCPU, 32GB RAM
- Test Duration: 7 days (multiple runs)
- Storage Types: Dedicated vs Shared
- Sites: Site Alpha & Site Beta

DEDICATED STORAGE PERFORMANCE:
-----------------------------
Site Alpha (Dedicated):
- Random Read IOPS: 12,500 IOPS (4K blocks)
- Random Write IOPS: 6,800 IOPS (4K blocks)
- Sequential Read: 580 MB/s
- Sequential Write: 420 MB/s
- Average Latency: 3.2ms (95th percentile)

Site Beta (Dedicated):
- Random Read IOPS: 11,800 IOPS (4K blocks)
- Random Write IOPS: 6,200 IOPS (4K blocks)
- Sequential Read: 550 MB/s
- Sequential Write: 380 MB/s
- Average Latency: 3.8ms (95th percentile)

SHARED STORAGE PERFORMANCE:
---------------------------
Site Alpha (Shared):
- Random Read IOPS: 8,500 IOPS (4K blocks)
- Random Write IOPS: 4,200 IOPS (4K blocks)
- Sequential Read: 420 MB/s
- Sequential Write: 280 MB/s
- Average Latency: 6.1ms (95th percentile)

Site Beta (Shared):
- Random Read IOPS: 7,800 IOPS (4K blocks)
- Random Write IOPS: 3,800 IOPS (4K blocks)
- Sequential Read: 380 MB/s
- Sequential Write: 250 MB/s
- Average Latency: 7.2ms (95th percentile)
```

### Enterprise DB Requirements Validation:
```
Requirements vs Results Comparison:
==================================

Random Read IOPS Requirement: >10,000 IOPS
✓ Dedicated Storage: MEETS REQUIREMENT (11,800-12,500 IOPS)
✗ Shared Storage: BELOW REQUIREMENT (7,800-8,500 IOPS)

Random Write IOPS Requirement: >5,000 IOPS
✓ Dedicated Storage: MEETS REQUIREMENT (6,200-6,800 IOPS)
✗ Shared Storage: BELOW REQUIREMENT (3,800-4,200 IOPS)

Sequential Read Requirement: >500 MB/s
✓ Dedicated Storage: MEETS REQUIREMENT (550-580 MB/s)
✗ Shared Storage: BELOW REQUIREMENT (380-420 MB/s)

Sequential Write Requirement: >300 MB/s
✓ Dedicated Storage: MEETS REQUIREMENT (380-420 MB/s)
✗ Shared Storage: BELOW REQUIREMENT (250-280 MB/s)

Latency Requirement: <5ms (95th percentile)
✓ Dedicated Storage: MEETS REQUIREMENT (3.2-3.8ms)
✗ Shared Storage: ABOVE REQUIREMENT (6.1-7.2ms)

RECOMMENDATION: Dedicated storage required for Enterprise DB workloads
```

## Automated Reporting & Documentation

### Technical Documentation Generation:
```bash
#!/bin/bash
# generate-technical-documentation.sh

generate_technical_documentation() {
    echo "Generating Technical Documentation"
    echo "================================="
    
    DOC_DIR="/benchmark/documentation"
    mkdir -p "$DOC_DIR"
    
    # Generate executive summary
    cat > "$DOC_DIR/executive-summary.md" << 'EOF'
# Storage Performance Benchmark - Executive Summary

## Overview
Comprehensive storage performance testing conducted for Enterprise Database (EDB) requirements validation on OpenStack infrastructure.

## Key Findings
- **Dedicated storage** meets all Enterprise DB performance requirements
- **Shared storage** suitable for development/testing but insufficient for production EDB
- **Site Alpha** shows slightly better performance than Site Beta
- **Consistent performance** across multiple test runs validates reliability

## Recommendations
1. **Production EDB**: Use dedicated storage instances
2. **Development/Testing**: Shared storage acceptable
3. **Performance Monitoring**: Implement continuous monitoring
4. **Capacity Planning**: Plan for 20% performance headroom

## Business Impact
- **Risk Mitigation**: Performance validation reduces deployment risk
- **Cost Optimization**: Right-sizing storage based on actual requirements
- **SLA Compliance**: Ensures performance SLA can be met
EOF
    
    echo "✓ Technical documentation generated"
}

generate_technical_documentation
```

## Kesimpulan

FIO storage benchmarking untuk enterprise database requirements membutuhkan **systematic approach** dan **comprehensive testing methodology**. Key takeaways:

1. **Dedicated storage** essential untuk meet enterprise DB performance requirements
2. **Comprehensive testing** important untuk validate all performance aspects
3. **Multi-site testing** provides confidence in infrastructure consistency
4. **Automated frameworks** enable repeatable dan reliable testing
5. **Clear documentation** critical untuk stakeholder communication

Yang paling valuable adalah **data-driven decision making** yang memungkinkan team untuk make informed choices tentang infrastructure sizing dan configuration untuk enterprise workloads.

Temen-temen punya pengalaman dengan storage benchmarking lainnya? Atau ada testing methodologies yang berbeda untuk database performance validation? Share di comments ya! 📊

---

*Benchmarking ini berdasarkan pengalaman comprehensive storage testing untuk Enterprise DB deployment di multi-site OpenStack infrastructure. Semua instance names dan performance data telah dianonymized untuk keamanan.*

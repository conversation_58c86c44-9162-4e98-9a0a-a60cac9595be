---
title: "XFS Disk Corruption Recovery: Emergency Database Repair Procedures untuk Production Systems"
date: "2025-01-12"
tags: ["disk-corruption", "xfs-repair", "database-recovery", "emergency-procedures", "data-recovery", "filesystem-repair"]
category: "Technology"
summary: "Emergency procedures untuk XFS disk corruption recovery di production database systems. Dari diagnosis hingga repair dengan minimal data loss dan downtime optimization."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Advanced"
keywords: ["xfs repair", "disk corruption recovery", "database emergency", "filesystem corruption", "data recovery", "production repair"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "XFS Disk Corruption Recovery: Emergency Database Repair Procedures untuk Production Systems"
  description: "Emergency procedures untuk XFS disk corruption recovery dengan systematic approach dan minimal data loss."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "XFS Disk Corruption Recovery: Emergency Database Repair"
  description: "Emergency procedures untuk XFS disk corruption recovery di production database systems."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "<PERSON><PERSON>"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang mengelola **production database systems**, salah satu nightmare scenario adalah **disk corruption** yang tiba-tiba terjadi di tengah malam. Ketika database critical tidak bisa mount karena **XFS filesystem corruption**, butuh **emergency response procedures** yang cepat dan reliable untuk minimize downtime dan data loss. 🚨

Di post ini, saya akan sharing pengalaman **emergency XFS disk corruption recovery** di production database dengan step-by-step procedures yang proven work untuk restore service availability dengan minimal impact.

## Emergency Incident Overview

### Critical Incident Details:
```
Incident Time: 00:49 WIB
Affected System: data-processing-app-03
Filesystem: /dev/mapper/vgdata2-lvdata2 (XFS)
Mount Point: /data2 (Database storage)
Application: Apache NiFi + Database
Impact: Service unavailable, data processing stopped
Severity: P1-Critical (Production service down)
```

### Initial Symptoms & Detection:
```bash
# Error symptoms detected
mount: /data2: mount(2) system call failed: Structure needs cleaning
dmesg: XFS (dm-1): Corruption detected. Unmount and run xfs_repair
systemctl status apache-nifi: Failed to start - data directory inaccessible
```

## Emergency Response Framework

### Phase 1: Immediate Assessment & Triage

**Critical System Assessment**:
```bash
#!/bin/bash
# emergency-disk-assessment.sh

perform_emergency_assessment() {
    echo "Emergency Disk Corruption Assessment"
    echo "==================================="
    echo "Assessment Time: $(date)"
    echo "Hostname: $(hostname)"
    echo
    
    # 1. Identify affected filesystem
    echo "1. Filesystem Assessment:"
    echo "   Checking mount status..."
    
    # Check mount status
    if mount | grep -q "/data2"; then
        echo "   ✓ /data2 currently mounted"
        MOUNTED=true
    else
        echo "   ✗ /data2 not mounted"
        MOUNTED=false
    fi
    
    # Check filesystem type and device
    DEVICE=$(grep "/data2" /etc/fstab | awk '{print $1}')
    FS_TYPE=$(grep "/data2" /etc/fstab | awk '{print $3}')
    
    echo "   Device: $DEVICE"
    echo "   Filesystem Type: $FS_TYPE"
    
    # 2. Check LVM status
    echo
    echo "2. LVM Status Assessment:"
    
    if command -v lvdisplay >/dev/null 2>&1; then
        echo "   LVM Volume Status:"
        lvdisplay "$DEVICE" 2>/dev/null | grep -E "LV Status|LV Size" | sed 's/^/      /'
        
        echo "   Volume Group Status:"
        vgdisplay vgdata2 2>/dev/null | grep -E "VG Status|VG Size" | sed 's/^/      /'
    fi
    
    # 3. Check for recent system events
    echo
    echo "3. Recent System Events:"
    echo "   Checking system logs for corruption indicators..."
    
    # Check dmesg for XFS errors
    dmesg | grep -i "xfs\|corruption\|error" | tail -5 | sed 's/^/      /'
    
    # Check system journal for mount errors
    journalctl --since "24 hours ago" | grep -i "mount\|xfs\|corruption" | tail -3 | sed 's/^/      /'
    
    # 4. Application impact assessment
    echo
    echo "4. Application Impact Assessment:"
    
    # Check Apache NiFi status
    if systemctl is-active apache-nifi >/dev/null 2>&1; then
        echo "   ✓ Apache NiFi: RUNNING"
    else
        echo "   ✗ Apache NiFi: STOPPED"
    fi
    
    # Check database processes
    if pgrep -f "database\|mysql\|postgres" >/dev/null 2>&1; then
        echo "   ✓ Database processes: RUNNING"
    else
        echo "   ✗ Database processes: STOPPED"
    fi
    
    # 5. Disk space and I/O assessment
    echo
    echo "5. Storage Assessment:"
    echo "   Available disk space:"
    df -h | grep -E "Filesystem|/dev/mapper" | sed 's/^/      /'
    
    echo "   I/O statistics:"
    iostat -x 1 1 | grep -E "Device|dm-" | sed 's/^/      /'
}

perform_emergency_assessment
```

### Phase 2: Pre-Repair Safety Measures

**Data Protection & Backup Verification**:
```bash
#!/bin/bash
# pre-repair-safety-measures.sh

implement_safety_measures() {
    echo "Implementing Pre-Repair Safety Measures"
    echo "======================================="
    
    # 1. Stop all services using the corrupted filesystem
    echo "1. Stopping dependent services..."
    
    SERVICES_TO_STOP=("apache-nifi" "database-service" "data-processor")
    
    for service in "${SERVICES_TO_STOP[@]}"; do
        if systemctl is-active "$service" >/dev/null 2>&1; then
            echo "   Stopping $service..."
            systemctl stop "$service"
            
            # Verify service stopped
            if systemctl is-active "$service" >/dev/null 2>&1; then
                echo "   ✗ Failed to stop $service"
            else
                echo "   ✓ $service stopped successfully"
            fi
        else
            echo "   - $service already stopped"
        fi
    done
    
    # 2. Unmount corrupted filesystem if still mounted
    echo
    echo "2. Unmounting corrupted filesystem..."
    
    if mount | grep -q "/data2"; then
        echo "   Attempting to unmount /data2..."
        
        # Force unmount if necessary
        if umount /data2 2>/dev/null; then
            echo "   ✓ /data2 unmounted successfully"
        else
            echo "   Attempting force unmount..."
            umount -f /data2 2>/dev/null || umount -l /data2
            echo "   ✓ /data2 force unmounted"
        fi
    else
        echo "   - /data2 already unmounted"
    fi
    
    # 3. Verify recent backups
    echo
    echo "3. Backup Verification:"
    
    BACKUP_LOCATIONS=("/backup/database" "/backup/nifi-data" "/backup/application-data")
    
    for backup_location in "${BACKUP_LOCATIONS[@]}"; do
        if [ -d "$backup_location" ]; then
            LATEST_BACKUP=$(find "$backup_location" -type f -name "*.tar.gz" -o -name "*.sql" | sort | tail -1)
            if [ -n "$LATEST_BACKUP" ]; then
                BACKUP_DATE=$(stat -c %y "$LATEST_BACKUP" | cut -d' ' -f1)
                echo "   ✓ Latest backup: $LATEST_BACKUP ($BACKUP_DATE)"
            else
                echo "   ✗ No recent backups found in $backup_location"
            fi
        else
            echo "   ✗ Backup location not found: $backup_location"
        fi
    done
    
    # 4. Create emergency snapshot if possible
    echo
    echo "4. Emergency Snapshot Creation:"
    
    # Check if LVM snapshot is possible
    VG_FREE=$(vgdisplay vgdata2 2>/dev/null | grep "Free" | awk '{print $7}' | sed 's/[<>]//g')
    
    if [ -n "$VG_FREE" ] && [ "${VG_FREE%.*}" -gt 1 ]; then
        echo "   Creating emergency LVM snapshot..."
        
        SNAPSHOT_NAME="lvdata2-emergency-$(date +%Y%m%d_%H%M%S)"
        
        if lvcreate -L1G -s -n "$SNAPSHOT_NAME" /dev/vgdata2/lvdata2 2>/dev/null; then
            echo "   ✓ Emergency snapshot created: $SNAPSHOT_NAME"
        else
            echo "   ✗ Failed to create emergency snapshot"
        fi
    else
        echo "   ✗ Insufficient space for emergency snapshot"
    fi
    
    # 5. Document current state
    echo
    echo "5. State Documentation:"
    
    # Create incident documentation
    INCIDENT_DIR="/tmp/disk-corruption-incident-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$INCIDENT_DIR"
    
    # Save system state
    dmesg > "$INCIDENT_DIR/dmesg-output.txt"
    journalctl --since "24 hours ago" > "$INCIDENT_DIR/system-journal.txt"
    mount > "$INCIDENT_DIR/mount-status.txt"
    lvdisplay > "$INCIDENT_DIR/lvm-status.txt"
    
    echo "   ✓ Incident documentation saved to: $INCIDENT_DIR"
}

implement_safety_measures
```

## XFS Repair Execution

### Phase 3: Systematic XFS Repair Process

**Comprehensive XFS Repair Procedure**:
```bash
#!/bin/bash
# xfs-repair-procedure.sh

execute_xfs_repair() {
    echo "Executing XFS Repair Procedure"
    echo "============================="
    echo "Repair Start Time: $(date)"
    
    DEVICE="/dev/mapper/vgdata2-lvdata2"
    REPAIR_LOG="/tmp/xfs-repair-$(date +%Y%m%d_%H%M%S).log"
    
    echo "Device: $DEVICE"
    echo "Repair Log: $REPAIR_LOG"
    echo
    
    # 1. Pre-repair filesystem check
    echo "1. Pre-repair Filesystem Analysis:"
    echo "   Performing read-only check..."
    
    # Read-only check to assess damage
    xfs_repair -n "$DEVICE" > "$REPAIR_LOG" 2>&1
    READONLY_EXIT_CODE=$?
    
    echo "   Read-only check exit code: $READONLY_EXIT_CODE"
    
    if [ $READONLY_EXIT_CODE -eq 0 ]; then
        echo "   ✓ Filesystem appears clean (false alarm?)"
        echo "   Attempting normal mount..."
        
        if mount "$DEVICE" /data2; then
            echo "   ✓ Mount successful - corruption may have been transient"
            return 0
        else
            echo "   ✗ Mount still fails - proceeding with repair"
        fi
    else
        echo "   ✗ Corruption confirmed - repair required"
    fi
    
    # 2. Execute repair with progress monitoring
    echo
    echo "2. Executing XFS Repair:"
    echo "   Starting repair process (this may take 30-60 minutes)..."
    echo "   Progress will be logged to: $REPAIR_LOG"
    
    # Start repair with verbose output
    (
        echo "XFS Repair Started: $(date)"
        echo "=================================="
        
        # Execute repair
        xfs_repair -v "$DEVICE"
        REPAIR_EXIT_CODE=$?
        
        echo
        echo "XFS Repair Completed: $(date)"
        echo "Exit Code: $REPAIR_EXIT_CODE"
        
        exit $REPAIR_EXIT_CODE
    ) >> "$REPAIR_LOG" 2>&1 &
    
    REPAIR_PID=$!
    
    # Monitor repair progress
    echo "   Repair PID: $REPAIR_PID"
    echo "   Monitoring progress..."
    
    while kill -0 $REPAIR_PID 2>/dev/null; do
        echo "   Repair in progress... ($(date))"
        
        # Show last few lines of repair log
        tail -3 "$REPAIR_LOG" | sed 's/^/      /'
        
        sleep 30
    done
    
    # Wait for repair to complete
    wait $REPAIR_PID
    FINAL_EXIT_CODE=$?
    
    echo
    echo "3. Repair Results:"
    echo "   Final exit code: $FINAL_EXIT_CODE"
    
    if [ $FINAL_EXIT_CODE -eq 0 ]; then
        echo "   ✓ XFS repair completed successfully"
    else
        echo "   ✗ XFS repair failed or encountered errors"
        echo "   Check repair log for details: $REPAIR_LOG"
        return 1
    fi
    
    # 4. Post-repair verification
    echo
    echo "4. Post-repair Verification:"
    
    # Verify filesystem integrity
    echo "   Verifying filesystem integrity..."
    if xfs_repair -n "$DEVICE" >/dev/null 2>&1; then
        echo "   ✓ Filesystem integrity verified"
    else
        echo "   ✗ Filesystem still has issues"
        return 1
    fi
    
    # Test mount
    echo "   Testing mount operation..."
    if mount "$DEVICE" /data2; then
        echo "   ✓ Mount successful"
        
        # Basic filesystem tests
        echo "   Performing basic filesystem tests..."
        
        # Test write operation
        if touch /data2/test-write-$(date +%s) 2>/dev/null; then
            echo "   ✓ Write test successful"
            rm -f /data2/test-write-* 2>/dev/null
        else
            echo "   ✗ Write test failed"
        fi
        
        # Check available space
        AVAILABLE_SPACE=$(df -h /data2 | tail -1 | awk '{print $4}')
        echo "   Available space: $AVAILABLE_SPACE"
        
    else
        echo "   ✗ Mount failed after repair"
        return 1
    fi
    
    echo
    echo "✓ XFS repair procedure completed successfully"
    return 0
}

execute_xfs_repair
```

### Phase 4: Service Recovery & Validation

**Application Service Recovery**:
```bash
#!/bin/bash
# service-recovery-procedure.sh

recover_application_services() {
    echo "Application Service Recovery"
    echo "==========================="
    echo "Recovery Start Time: $(date)"
    
    # 1. Filesystem validation
    echo "1. Final Filesystem Validation:"
    
    if ! mount | grep -q "/data2"; then
        echo "   ✗ /data2 not mounted - mounting now..."
        if ! mount /dev/mapper/vgdata2-lvdata2 /data2; then
            echo "   ✗ Failed to mount /data2"
            return 1
        fi
    fi
    
    echo "   ✓ /data2 mounted successfully"
    
    # Check filesystem health
    echo "   Checking filesystem health..."
    FS_USAGE=$(df -h /data2 | tail -1 | awk '{print $5}' | sed 's/%//')
    echo "   Filesystem usage: ${FS_USAGE}%"
    
    if [ "$FS_USAGE" -gt 90 ]; then
        echo "   ⚠ Warning: Filesystem usage >90%"
    fi
    
    # 2. Data integrity verification
    echo
    echo "2. Data Integrity Verification:"
    
    # Check critical application directories
    CRITICAL_DIRS=("/data2/nifi" "/data2/database" "/data2/logs")
    
    for dir in "${CRITICAL_DIRS[@]}"; do
        if [ -d "$dir" ]; then
            FILE_COUNT=$(find "$dir" -type f | wc -l)
            echo "   ✓ $dir: $FILE_COUNT files found"
        else
            echo "   ✗ $dir: Directory missing"
        fi
    done
    
    # Check for recent data files
    echo "   Checking for recent data files..."
    RECENT_FILES=$(find /data2 -type f -mtime -1 | wc -l)
    echo "   Recent files (last 24h): $RECENT_FILES"
    
    # 3. Application service startup
    echo
    echo "3. Application Service Startup:"
    
    # Start services in correct order
    SERVICES_TO_START=("database-service" "apache-nifi" "data-processor")
    
    for service in "${SERVICES_TO_START[@]}"; do
        echo "   Starting $service..."
        
        if systemctl start "$service"; then
            # Wait for service to stabilize
            sleep 10
            
            if systemctl is-active "$service" >/dev/null 2>&1; then
                echo "   ✓ $service started successfully"
            else
                echo "   ✗ $service failed to start properly"
                
                # Show service status for debugging
                systemctl status "$service" --no-pager -l | head -10 | sed 's/^/      /'
            fi
        else
            echo "   ✗ Failed to start $service"
        fi
    done
    
    # 4. Application functionality testing
    echo
    echo "4. Application Functionality Testing:"
    
    # Test Apache NiFi web interface
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/nifi | grep -q "200"; then
        echo "   ✓ Apache NiFi web interface: ACCESSIBLE"
    else
        echo "   ✗ Apache NiFi web interface: NOT ACCESSIBLE"
    fi
    
    # Test database connectivity
    if command -v mysql >/dev/null 2>&1; then
        if mysql -e "SELECT 1;" >/dev/null 2>&1; then
            echo "   ✓ Database connectivity: WORKING"
        else
            echo "   ✗ Database connectivity: FAILED"
        fi
    fi
    
    # Test data processing functionality
    if [ -f "/data2/nifi/logs/nifi-app.log" ]; then
        RECENT_LOGS=$(tail -10 /data2/nifi/logs/nifi-app.log | grep -c "$(date +%Y-%m-%d)")
        if [ "$RECENT_LOGS" -gt 0 ]; then
            echo "   ✓ Data processing: ACTIVE (recent log entries found)"
        else
            echo "   ⚠ Data processing: NO RECENT ACTIVITY"
        fi
    fi
    
    # 5. Performance validation
    echo
    echo "5. Performance Validation:"
    
    # I/O performance test
    echo "   Testing I/O performance..."
    IO_TEST_RESULT=$(dd if=/dev/zero of=/data2/io-test bs=1M count=100 2>&1 | grep "MB/s" | awk '{print $8" "$9}')
    echo "   I/O performance: $IO_TEST_RESULT"
    rm -f /data2/io-test
    
    # Memory usage check
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')
    echo "   Memory usage: $MEMORY_USAGE"
    
    # Load average check
    LOAD_AVERAGE=$(uptime | awk -F'load average:' '{print $2}')
    echo "   Load average:$LOAD_AVERAGE"
    
    echo
    echo "✓ Service recovery completed successfully"
}

recover_application_services
```

## Post-Recovery Monitoring & Prevention

### Continuous Health Monitoring:
```bash
#!/bin/bash
# post-recovery-monitoring.sh

implement_post_recovery_monitoring() {
    echo "Implementing Post-Recovery Monitoring"
    echo "===================================="
    
    # 1. Filesystem health monitoring
    cat > /opt/monitoring/xfs-health-monitor.sh << 'EOF'
#!/bin/bash
# XFS filesystem health monitoring

LOGFILE="/var/log/xfs-health-monitor.log"
DEVICE="/dev/mapper/vgdata2-lvdata2"

monitor_xfs_health() {
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Check filesystem errors in dmesg
    XFS_ERRORS=$(dmesg | grep -i "xfs.*error\|xfs.*corruption" | wc -l)
    
    # Check filesystem usage
    FS_USAGE=$(df /data2 | tail -1 | awk '{print $5}' | sed 's/%//')
    
    # Check I/O errors
    IO_ERRORS=$(iostat -x 1 1 | grep dm- | awk '{sum+=$10} END {print sum+0}')
    
    # Log status
    echo "$TIMESTAMP,XFS_ERRORS:$XFS_ERRORS,FS_USAGE:${FS_USAGE}%,IO_ERRORS:$IO_ERRORS" >> "$LOGFILE"
    
    # Alert on issues
    if [ "$XFS_ERRORS" -gt 0 ]; then
        logger "XFS HEALTH ALERT: Filesystem errors detected on $DEVICE"
    fi
    
    if [ "$FS_USAGE" -gt 85 ]; then
        logger "XFS HEALTH ALERT: Filesystem usage >85% on /data2"
    fi
}

monitor_xfs_health
EOF
    
    chmod +x /opt/monitoring/xfs-health-monitor.sh
    
    # Schedule monitoring
    echo "*/5 * * * * /opt/monitoring/xfs-health-monitor.sh" | crontab -
    
    echo "   ✓ XFS health monitoring implemented"
    
    # 2. Application health monitoring
    cat > /opt/monitoring/application-health-monitor.sh << 'EOF'
#!/bin/bash
# Application health monitoring post-recovery

LOGFILE="/var/log/application-health-monitor.log"

monitor_application_health() {
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Check service status
    NIFI_STATUS=$(systemctl is-active apache-nifi)
    DB_STATUS=$(systemctl is-active database-service)
    
    # Check data processing activity
    if [ -f "/data2/nifi/logs/nifi-app.log" ]; then
        RECENT_ACTIVITY=$(tail -100 /data2/nifi/logs/nifi-app.log | grep -c "$(date +%Y-%m-%d)")
    else
        RECENT_ACTIVITY=0
    fi
    
    # Log status
    echo "$TIMESTAMP,NIFI:$NIFI_STATUS,DB:$DB_STATUS,ACTIVITY:$RECENT_ACTIVITY" >> "$LOGFILE"
    
    # Alert on issues
    if [ "$NIFI_STATUS" != "active" ]; then
        logger "APPLICATION HEALTH ALERT: Apache NiFi not active"
    fi
    
    if [ "$RECENT_ACTIVITY" -eq 0 ]; then
        logger "APPLICATION HEALTH ALERT: No recent data processing activity"
    fi
}

monitor_application_health
EOF
    
    chmod +x /opt/monitoring/application-health-monitor.sh
    
    # Schedule application monitoring
    echo "*/10 * * * * /opt/monitoring/application-health-monitor.sh" | crontab -
    
    echo "   ✓ Application health monitoring implemented"
}

implement_post_recovery_monitoring
```

## Recovery Results & Lessons Learned

### Incident Resolution Summary:
```
XFS Disk Corruption Recovery Results:
====================================

Incident Duration: 2.5 hours (00:49 - 03:20 WIB)
Recovery Method: xfs_repair with systematic approach
Data Loss: Zero (all data recovered successfully)
Service Downtime: 2.5 hours (within acceptable SLA)

Recovery Timeline:
- 00:49: Incident detected and reported
- 01:15: Emergency assessment completed
- 01:30: Safety measures implemented
- 02:00: XFS repair initiated
- 02:45: Repair completed successfully
- 03:20: All services restored and validated

Success Metrics:
✓ Complete data recovery
✓ All services operational
✓ Performance baseline restored
✓ No secondary corruption detected
✓ Monitoring implemented for prevention
```

### Root Cause Analysis:
```
Identified Contributing Factors:
1. Unexpected system reboot during active I/O operations
2. No filesystem journaling protection for specific workload
3. Insufficient monitoring for early corruption detection
4. Missing automated backup verification procedures

Prevention Measures Implemented:
1. Enhanced filesystem monitoring with automated alerts
2. Improved backup validation procedures
3. Application graceful shutdown procedures
4. Regular filesystem health checks
5. Emergency response documentation updated
```

## Best Practices & Prevention

### Proactive Measures Checklist:
```bash
# XFS filesystem protection checklist

□ Regular filesystem health monitoring
□ Automated backup verification
□ Graceful application shutdown procedures
□ Emergency response documentation
□ Staff training on recovery procedures
□ Regular recovery procedure testing
□ Monitoring for early corruption indicators
□ Proper UPS protection for critical systems
□ Regular system maintenance windows
□ Documentation of all recovery procedures
```

## Kesimpulan

XFS disk corruption recovery membutuhkan **systematic emergency response** dan **careful execution** untuk ensure successful data recovery dengan minimal downtime. Key takeaways:

1. **Quick assessment** critical untuk understand corruption scope
2. **Safety measures** essential sebelum attempt repair
3. **Systematic repair approach** minimize risk of additional data loss
4. **Thorough validation** important untuk ensure complete recovery
5. **Proactive monitoring** essential untuk prevent future incidents

Yang paling valuable adalah **preparation** dan **documented procedures** yang enable rapid response ketika emergency situations occur.

Temen-temen punya pengalaman dengan filesystem corruption lainnya? Atau ada recovery techniques yang berbeda untuk XFS atau filesystem lain? Share di comments ya! 🚨

---

*Recovery procedure ini berdasarkan pengalaman real emergency response untuk XFS corruption di production database system. Semua system names dan identifiers telah dianonymized untuk keamanan.*

---
title: "Automasi Reporting: OpenStack Utilization dengan Google Sheets API dan Cron Jobs"
date: "2025-01-12"
tags: ["automation", "openstack", "google-sheets", "scripting", "reporting", "cron", "python", "api-integration"]
category: "Technology"
summary: "Pengalaman membangun sistem automasi reporting monthly utilization OpenStack yang terintegrasi dengan Google Sheets API. Dari manual spreadsheet ke fully automated reporting dengan cron jobs dan error handling."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Intermediate"
keywords: ["openstack automation", "google sheets api", "automated reporting", "cron jobs", "python scripting", "infrastructure reporting"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "Automasi Reporting: OpenStack Utilization dengan Google Sheets API dan Cron Jobs"
  description: "Membangun sistem automasi reporting monthly utilization OpenStack dengan Google Sheets API dan cron jobs untuk efisiensi operational."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "Automasi Reporting: OpenStack Utilization dengan Google Sheets API"
  description: "Automasi reporting monthly utilization OpenStack dengan Google Sheets API dan cron jobs."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "Febryan Ramadhan"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang mengelola infrastructure cloud, salah satu task rutin yang paling time-consuming adalah **monthly reporting**. Bayangkan temen-temen harus manual collect data utilization dari multiple OpenStack sites, compile ke spreadsheet, format dengan rapi, dan deliver ke management setiap bulan. Repetitive banget kan? 😅

Di post ini, saya akan sharing pengalaman membangun **automated reporting system** yang mengintegrasikan OpenStack API dengan Google Sheets untuk generate monthly utilization reports. Project ini menghemat 8+ jam manual work per bulan dan meningkatkan accuracy reporting significantly.

## Problem Statement

### Manual Process Pain Points:
- **8+ hours** manual work setiap bulan
- **Human errors** dalam data collection dan formatting
- **Inconsistent formatting** antar monthly reports
- **Delayed delivery** karena complexity manual process
- **No historical tracking** yang systematic

### Business Requirements:
- **Monthly utilization reports** untuk management
- **Multi-site data** (GTI dan ODC data centers)
- **Standardized format** dengan company branding
- **Automated delivery** di akhir bulan
- **Historical data** untuk trend analysis

## Solution Architecture

Kami design automated reporting system dengan components berikut:

```
┌─────────────────┐    ┌─────────────────┐
│   Site GTI      │    │   Site ODC      │
│                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ OpenStack   │ │    │ │ OpenStack   │ │
│ │ API         │ │    │ │ API         │ │
│ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
              ┌─────────────┐
              │ Automation  │
              │ Scripts     │
              │             │
              │ - Data      │
              │   Collection│
              │ - Processing│
              │ - Formatting│
              └─────────────┘
                     │
              ┌─────────────┐
              │ Google      │
              │ Sheets API  │
              │             │
              │ - Template  │
              │ - Data      │
              │ - Formatting│
              └─────────────┘
```

### Technology Stack:
- **OpenStack CLI** untuk data collection
- **Python** untuk data processing
- **Google Sheets API** untuk report generation
- **Cron Jobs** untuk scheduling
- **Bash Scripts** untuk orchestration

## Implementation Details

### Phase 1: Data Collection Script

Pertama, kami develop script untuk collect utilization data dari OpenStack:

```bash
#!/bin/bash
# host-util.sh - Main orchestration script

SITE=$(hostname | grep -q "gti" && echo "GTI" || echo "ODC")
MONTH=$(date +"%B-%Y")
WORKDIR="/workdir/script-host-utilization"
LOGFILE="$WORKDIR/host-util.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGFILE"
}

collect_hypervisor_data() {
    log "Collecting hypervisor utilization data for $SITE"
    
    # Get hypervisor statistics
    openstack hypervisor stats show -f json > "$WORKDIR/hypervisor-stats-$SITE.json"
    
    # Get detailed hypervisor list
    openstack hypervisor list --long -f json > "$WORKDIR/hypervisor-list-$SITE.json"
    
    # Get flavor information
    openstack flavor list -f json > "$WORKDIR/flavor-list-$SITE.json"
    
    # Get instance statistics
    openstack server list --all-projects -f json > "$WORKDIR/instance-list-$SITE.json"
}

process_utilization_data() {
    log "Processing utilization data for $SITE"
    
    python3 "$WORKDIR/process_utilization.py" \
        --site "$SITE" \
        --month "$MONTH" \
        --hypervisor-stats "$WORKDIR/hypervisor-stats-$SITE.json" \
        --hypervisor-list "$WORKDIR/hypervisor-list-$SITE.json" \
        --instance-list "$WORKDIR/instance-list-$SITE.json" \
        --output "$WORKDIR/utilization-data-$SITE.json"
}

main() {
    log "Starting monthly utilization report generation for $SITE"
    
    # Source OpenStack credentials
    source /home/<USER>/admin-openrc
    
    # Collect data
    collect_hypervisor_data
    
    # Process data
    process_utilization_data
    
    # Upload to Google Sheets (GTI only creates new sheet)
    if [ "$SITE" = "GTI" ]; then
        log "Creating new Google Sheet for $MONTH"
        python3 "$WORKDIR/create_monthly_sheet.py" --month "$MONTH"
    fi
    
    # Upload site data
    log "Uploading $SITE data to Google Sheets"
    python3 "$WORKDIR/upload_to_sheets.py" \
        --site "$SITE" \
        --month "$MONTH" \
        --data "$WORKDIR/utilization-data-$SITE.json"
    
    log "Monthly utilization report generation completed for $SITE"
}

main "$@"
```

### Phase 2: Data Processing Script

Python script untuk process raw OpenStack data:

```python
#!/usr/bin/env python3
# process_utilization.py

import json
import argparse
from datetime import datetime
from collections import defaultdict

class UtilizationProcessor:
    def __init__(self, site, month):
        self.site = site
        self.month = month
        self.data = {
            'site': site,
            'month': month,
            'generated_at': datetime.now().isoformat(),
            'hypervisors': [],
            'summary': {}
        }
    
    def load_json_file(self, filepath):
        """Load JSON data from file"""
        try:
            with open(filepath, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading {filepath}: {e}")
            return {}
    
    def process_hypervisor_data(self, stats_file, list_file):
        """Process hypervisor statistics and details"""
        stats = self.load_json_file(stats_file)
        hypervisors = self.load_json_file(list_file)
        
        # Process overall statistics
        if stats:
            self.data['summary'] = {
                'total_hypervisors': stats.get('count', 0),
                'total_vcpus': stats.get('vcpus', 0),
                'used_vcpus': stats.get('vcpus_used', 0),
                'total_memory_mb': stats.get('memory_mb', 0),
                'used_memory_mb': stats.get('memory_mb_used', 0),
                'total_disk_gb': stats.get('local_gb', 0),
                'used_disk_gb': stats.get('local_gb_used', 0),
                'running_vms': stats.get('running_vms', 0)
            }
            
            # Calculate utilization percentages
            self.data['summary']['vcpu_utilization'] = round(
                (self.data['summary']['used_vcpus'] / self.data['summary']['total_vcpus']) * 100, 2
            ) if self.data['summary']['total_vcpus'] > 0 else 0
            
            self.data['summary']['memory_utilization'] = round(
                (self.data['summary']['used_memory_mb'] / self.data['summary']['total_memory_mb']) * 100, 2
            ) if self.data['summary']['total_memory_mb'] > 0 else 0
            
            self.data['summary']['disk_utilization'] = round(
                (self.data['summary']['used_disk_gb'] / self.data['summary']['total_disk_gb']) * 100, 2
            ) if self.data['summary']['total_disk_gb'] > 0 else 0
        
        # Process individual hypervisor details
        for hypervisor in hypervisors:
            if hypervisor.get('State') == 'up':
                hypervisor_data = {
                    'name': hypervisor.get('Hypervisor Hostname', ''),
                    'type': hypervisor.get('Hypervisor Type', ''),
                    'vcpus': hypervisor.get('vCPUs', 0),
                    'vcpus_used': hypervisor.get('vCPUs Used', 0),
                    'memory_mb': hypervisor.get('Memory MB', 0),
                    'memory_used_mb': hypervisor.get('Memory MB Used', 0),
                    'disk_gb': hypervisor.get('Disk GB', 0),
                    'disk_used_gb': hypervisor.get('Disk GB Used', 0),
                    'running_vms': hypervisor.get('Running VMs', 0)
                }
                
                # Calculate individual utilization
                hypervisor_data['vcpu_utilization'] = round(
                    (hypervisor_data['vcpus_used'] / hypervisor_data['vcpus']) * 100, 2
                ) if hypervisor_data['vcpus'] > 0 else 0
                
                hypervisor_data['memory_utilization'] = round(
                    (hypervisor_data['memory_used_mb'] / hypervisor_data['memory_mb']) * 100, 2
                ) if hypervisor_data['memory_mb'] > 0 else 0
                
                self.data['hypervisors'].append(hypervisor_data)
    
    def process_instance_data(self, instance_file):
        """Process instance statistics by project"""
        instances = self.load_json_file(instance_file)
        
        project_stats = defaultdict(lambda: {
            'total_instances': 0,
            'active_instances': 0,
            'total_vcpus': 0,
            'total_memory_gb': 0,
            'total_disk_gb': 0
        })
        
        for instance in instances:
            project = instance.get('Project', 'unknown')
            status = instance.get('Status', '')
            flavor = instance.get('Flavor', '')
            
            project_stats[project]['total_instances'] += 1
            if status.lower() == 'active':
                project_stats[project]['active_instances'] += 1
                
                # Parse flavor information (would need flavor details)
                # This is simplified - in real implementation, 
                # you'd lookup flavor details for accurate resource counting
        
        self.data['projects'] = dict(project_stats)
    
    def save_data(self, output_file):
        """Save processed data to JSON file"""
        try:
            with open(output_file, 'w') as f:
                json.dump(self.data, f, indent=2)
            print(f"Processed data saved to {output_file}")
        except Exception as e:
            print(f"Error saving data: {e}")

def main():
    parser = argparse.ArgumentParser(description='Process OpenStack utilization data')
    parser.add_argument('--site', required=True, help='Site name (GTI/ODC)')
    parser.add_argument('--month', required=True, help='Month-Year (e.g., January-2024)')
    parser.add_argument('--hypervisor-stats', required=True, help='Hypervisor stats JSON file')
    parser.add_argument('--hypervisor-list', required=True, help='Hypervisor list JSON file')
    parser.add_argument('--instance-list', required=True, help='Instance list JSON file')
    parser.add_argument('--output', required=True, help='Output JSON file')
    
    args = parser.parse_args()
    
    processor = UtilizationProcessor(args.site, args.month)
    processor.process_hypervisor_data(args.hypervisor_stats, args.hypervisor_list)
    processor.process_instance_data(args.instance_list)
    processor.save_data(args.output)

if __name__ == '__main__':
    main()
```

### Phase 3: Google Sheets Integration

Script untuk integrate dengan Google Sheets API:

```python
#!/usr/bin/env python3
# upload_to_sheets.py

import json
import argparse
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

class SheetsUploader:
    def __init__(self, credentials_file, spreadsheet_id):
        self.credentials = Credentials.from_service_account_file(
            credentials_file,
            scopes=['https://www.googleapis.com/auth/spreadsheets']
        )
        self.service = build('sheets', 'v4', credentials=self.credentials)
        self.spreadsheet_id = spreadsheet_id
    
    def create_monthly_sheet(self, month):
        """Create new sheet for the month"""
        try:
            sheet_metadata = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()
            
            # Check if sheet already exists
            sheet_names = [sheet['properties']['title'] for sheet in sheet_metadata['sheets']]
            if month in sheet_names:
                print(f"Sheet '{month}' already exists")
                return
            
            # Create new sheet
            request_body = {
                'requests': [{
                    'addSheet': {
                        'properties': {
                            'title': month,
                            'gridProperties': {
                                'rowCount': 100,
                                'columnCount': 20
                            }
                        }
                    }
                }]
            }
            
            response = self.service.spreadsheets().batchUpdate(
                spreadsheetId=self.spreadsheet_id,
                body=request_body
            ).execute()
            
            print(f"Created new sheet: {month}")
            
            # Setup headers and formatting
            self.setup_sheet_template(month)
            
        except HttpError as e:
            print(f"Error creating sheet: {e}")
    
    def setup_sheet_template(self, sheet_name):
        """Setup template headers and formatting"""
        headers = [
            ['Monthly Infrastructure Utilization Report'],
            ['Site', 'GTI', 'ODC'],
            ['Month', '', ''],
            [''],
            ['Summary Statistics'],
            ['Metric', 'GTI', 'ODC'],
            ['Total Hypervisors', '', ''],
            ['Total vCPUs', '', ''],
            ['Used vCPUs', '', ''],
            ['vCPU Utilization %', '', ''],
            ['Total Memory (GB)', '', ''],
            ['Used Memory (GB)', '', ''],
            ['Memory Utilization %', '', ''],
            ['Total Disk (GB)', '', ''],
            ['Used Disk (GB)', '', ''],
            ['Disk Utilization %', '', ''],
            ['Running VMs', '', '']
        ]
        
        # Upload headers
        range_name = f"{sheet_name}!A1:C17"
        body = {'values': headers}
        
        try:
            self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range=range_name,
                valueInputOption='RAW',
                body=body
            ).execute()
            
            print(f"Template setup completed for {sheet_name}")
            
        except HttpError as e:
            print(f"Error setting up template: {e}")
    
    def upload_site_data(self, site, month, data_file):
        """Upload site-specific data to the sheet"""
        try:
            with open(data_file, 'r') as f:
                data = json.load(f)
            
            # Determine column based on site
            col = 'B' if site == 'GTI' else 'C'
            
            # Prepare data values
            values = [
                [data['summary']['total_hypervisors']],
                [data['summary']['total_vcpus']],
                [data['summary']['used_vcpus']],
                [f"{data['summary']['vcpu_utilization']}%"],
                [round(data['summary']['total_memory_mb'] / 1024, 2)],  # Convert to GB
                [round(data['summary']['used_memory_mb'] / 1024, 2)],
                [f"{data['summary']['memory_utilization']}%"],
                [data['summary']['total_disk_gb']],
                [data['summary']['used_disk_gb']],
                [f"{data['summary']['disk_utilization']}%"],
                [data['summary']['running_vms']]
            ]
            
            # Upload data
            range_name = f"{month}!{col}7:{col}17"
            body = {'values': values}
            
            self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range=range_name,
                valueInputOption='RAW',
                body=body
            ).execute()
            
            # Update month in header
            month_range = f"{month}!{col}3"
            month_body = {'values': [[data['month']]]}
            
            self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range=month_range,
                valueInputOption='RAW',
                body=month_body
            ).execute()
            
            print(f"Uploaded {site} data to {month} sheet")
            
        except Exception as e:
            print(f"Error uploading {site} data: {e}")

def main():
    parser = argparse.ArgumentParser(description='Upload utilization data to Google Sheets')
    parser.add_argument('--site', required=True, help='Site name (GTI/ODC)')
    parser.add_argument('--month', required=True, help='Month-Year')
    parser.add_argument('--data', required=True, help='Data JSON file')
    
    args = parser.parse_args()
    
    # Configuration
    CREDENTIALS_FILE = '/workdir/script-host-utilization/service-account.json'
    SPREADSHEET_ID = 'your-spreadsheet-id-here'
    
    uploader = SheetsUploader(CREDENTIALS_FILE, SPREADSHEET_ID)
    uploader.upload_site_data(args.site, args.month, args.data)

if __name__ == '__main__':
    main()
```

## Cron Job Implementation

Setup automated execution dengan cron jobs:

```bash
# Cron configuration untuk end-of-month execution
# GTI site - runs at 23:40 on the 28th if next day is different month
40 23 28-31 * * [ "$(date +\%m -d tomorrow)" != "$(date +\%m)" ] && /home/<USER>/workdir/script-host-utilization/host-util.sh >> /home/<USER>/workdir/script-host-utilization/host-util.log 2>&1

# ODC site - runs at 23:50 on the 28th if next day is different month  
50 23 28-31 * * [ "$(date +\%m -d tomorrow)" != "$(date +\%m)" ] && /home/<USER>/workdir/script-host-utilization/host-util.sh >> /home/<USER>/workdir/script-host-utilization/host-util.log 2>&1
```

**Cron Logic Explanation**:
- **28-31**: Run pada tanggal 28-31 setiap bulan
- **Date check**: `[ "$(date +\%m -d tomorrow)" != "$(date +\%m)" ]` memastikan hanya run di akhir bulan
- **10-minute delay**: GTI runs first (23:40), ODC follows (23:50) untuk avoid conflicts

## Production Challenges & Solutions

### Challenge 1: Script Execution Failures

**Problem**: Cron job berjalan tapi script berhenti saat data population.

**Debugging Process**:
```bash
# Check cron execution
tail -f /var/log/cron

# Check script logs
tail -f /workdir/script-host-utilization/host-util.log

# Manual execution untuk debugging
bash -x /workdir/script-host-utilization/host-util.sh
```

**Root Cause**: OpenStack API timeout dan network connectivity issues.

**Solution**: Implement retry logic dan error handling:

```bash
# Enhanced error handling
retry_command() {
    local max_attempts=3
    local delay=5
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if "$@"; then
            return 0
        else
            log "Attempt $attempt failed. Retrying in $delay seconds..."
            sleep $delay
            ((attempt++))
            delay=$((delay * 2))  # Exponential backoff
        fi
    done
    
    log "Command failed after $max_attempts attempts: $*"
    return 1
}

# Usage
retry_command openstack hypervisor stats show -f json
```

### Challenge 2: Google Sheets API Rate Limits

**Problem**: API calls failing karena rate limiting.

**Solution**: Implement rate limiting dan batch operations:

```python
import time
from functools import wraps

def rate_limit(calls_per_minute=100):
    """Decorator untuk rate limiting API calls"""
    min_interval = 60.0 / calls_per_minute
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator

# Apply rate limiting
@rate_limit(calls_per_minute=60)
def sheets_api_call(self, method, **kwargs):
    return method(**kwargs)
```

## Results & Impact

### Quantitative Improvements:
- **Time savings**: 8+ hours → 15 minutes per month
- **Accuracy**: 100% consistent data formatting
- **Delivery time**: Same day vs 2-3 days delay
- **Error reduction**: 90% fewer manual errors

### Qualitative Benefits:
- **Consistent reporting** format dan branding
- **Historical tracking** dengan automated archiving
- **Stakeholder satisfaction** dengan timely delivery
- **Team productivity** untuk focus ke strategic tasks

## Best Practices Learned

### 1. Error Handling & Logging
```bash
# Comprehensive logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$1] $2" | tee -a "$LOGFILE"
}

# Usage
log "INFO" "Starting data collection"
log "ERROR" "Failed to connect to OpenStack API"
log "SUCCESS" "Data uploaded to Google Sheets"
```

### 2. Configuration Management
```bash
# Centralized configuration
CONFIG_FILE="/workdir/script-host-utilization/config.conf"

# Load configuration
source "$CONFIG_FILE"

# Example config.conf
OPENSTACK_RC_FILE="/home/<USER>/admin-openrc"
GOOGLE_CREDENTIALS="/workdir/script-host-utilization/service-account.json"
SPREADSHEET_ID="1FpzYOR-kZxEnuww2oxwn3PS7TOyFYne_agKztgdq_ck"
MAX_RETRIES=3
API_DELAY=2
```

### 3. Monitoring & Alerting
```bash
# Add monitoring untuk script execution
if [ $? -eq 0 ]; then
    log "SUCCESS" "Monthly report generation completed successfully"
    # Send success notification
    curl -X POST "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
         -H 'Content-type: application/json' \
         --data '{"text":"Monthly utilization report generated successfully for '$SITE'"}'
else
    log "ERROR" "Monthly report generation failed"
    # Send failure alert
    curl -X POST "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
         -H 'Content-type: application/json' \
         --data '{"text":"ALERT: Monthly utilization report failed for '$SITE'. Check logs."}'
fi
```

## Future Enhancements

### Planned Improvements:
1. **Real-time dashboards** dengan live data updates
2. **Predictive analytics** untuk capacity planning
3. **Multi-format exports** (PDF, Excel, PowerBI)
4. **Advanced visualizations** dengan charts dan graphs

### Integration Opportunities:
- **ITSM integration** untuk automated ticket creation
- **BI tools** untuk advanced analytics
- **Cost optimization** recommendations
- **Capacity planning** alerts

## Kesimpulan

Implementasi automated reporting system ini memberikan significant improvement dalam operational efficiency. Key takeaways:

1. **Automation** dramatically reduces manual effort dan errors
2. **API integration** enables powerful workflow automation
3. **Proper error handling** essential untuk production reliability
4. **Monitoring** crucial untuk maintaining automated systems

Yang paling valuable adalah **shift from reactive to proactive** reporting. Sekarang management mendapat consistent, timely reports yang enable better decision making.

Temen-temen punya pengalaman dengan reporting automation lainnya? Atau ada challenge khusus dalam API integration? Share di comments ya! 📊

---

*Artikel ini berdasarkan pengalaman implementasi automated reporting untuk infrastructure cloud dengan 200+ hypervisors. Semua credentials dan identifiers telah dianonymized untuk keamanan.*

---
title: "Kernel Upgrade Troubleshooting: Fixing Network Connectivity Issues Post-Upgrade"
date: "2025-01-12"
tags: ["kernel-upgrade", "troubleshooting", "networking", "dns", "proxy", "ubuntu", "quick-fix"]
category: "Technology"
summary: "Troubleshooting network connectivity issues setelah kernel upgrade di Ubuntu instances. Dari DNS resolution failure hingga proxy configuration fixes dengan step-by-step solution."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Intermediate"
keywords: ["kernel upgrade", "network troubleshooting", "dns resolution", "proxy configuration", "ubuntu networking", "netplan configuration"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "Kernel Upgrade Troubleshooting: Fixing Network Connectivity Issues Post-Upgrade"
  description: "Step-by-step troubleshooting network connectivity issues setelah kernel upgrade dengan DNS dan proxy configuration fixes."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "Kernel Upgrade Troubleshooting: Network Connectivity Fixes"
  description: "Troubleshooting network connectivity issues post kernel upgrade dengan DNS dan proxy configuration."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "<PERSON><PERSON>"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai SRE yang sering handle **kernel upgrades** di production environment, salah satu issue yang paling tricky adalah **network connectivity problems** yang muncul setelah upgrade. Bayangkan temen-temen baru selesai upgrade kernel, tapi tiba-tiba instance tidak bisa connect ke internet - padahal sebelumnya normal! 🔧

Di post ini, saya akan sharing pengalaman **troubleshooting network connectivity issues** setelah kernel upgrade di Ubuntu instances, dengan step-by-step solution yang proven work di production environment.

## Problem Discovery

### Initial Situation:
```
Target Instance: app-database-staging-02
Original Kernel: 5.4.0-72-generic
Target Kernel: 5.4.0-122-generic
OS: Ubuntu 18.04.5 LTS
Issue: Network connectivity lost after kernel upgrade
```

### Symptoms Detected:
- `apt update` command failed
- DNS resolution not working
- Internet connectivity completely lost
- Instance accessible via console but no external network

## Systematic Troubleshooting Approach

### Phase 1: Initial Connectivity Assessment

**Basic Network Tests**:
```bash
#!/bin/bash
# network-connectivity-assessment.sh

perform_initial_assessment() {
    echo "Network Connectivity Assessment"
    echo "=============================="
    echo "Timestamp: $(date)"
    echo "Hostname: $(hostname)"
    echo "Kernel: $(uname -r)"
    echo
    
    # Test 1: Basic internet connectivity
    echo "1. Testing Internet Connectivity:"
    if curl -s --connect-timeout 5 google.com >/dev/null 2>&1; then
        echo "   ✓ Internet connectivity: WORKING"
    else
        echo "   ✗ Internet connectivity: FAILED"
    fi
    
    # Test 2: DNS resolution
    echo "2. Testing DNS Resolution:"
    if nslookup google.com >/dev/null 2>&1; then
        echo "   ✓ DNS resolution: WORKING"
    else
        echo "   ✗ DNS resolution: FAILED"
    fi
    
    # Test 3: Package manager connectivity
    echo "3. Testing Package Manager:"
    if timeout 10 apt update >/dev/null 2>&1; then
        echo "   ✓ APT connectivity: WORKING"
    else
        echo "   ✗ APT connectivity: FAILED"
    fi
    
    echo
    echo "Detailed Network Analysis:"
    echo "========================="
    
    # Network interface status
    echo "Network Interfaces:"
    ip addr show | grep -E "^[0-9]+:|inet " | sed 's/^/  /'
    
    # Routing table
    echo
    echo "Routing Table:"
    ip route show | sed 's/^/  /'
    
    # DNS configuration
    echo
    echo "DNS Configuration:"
    resolvectl status | grep -E "DNS Servers|Current DNS" | sed 's/^/  /'
}

perform_initial_assessment
```

**Results from Assessment**:
```
Network Connectivity Assessment
==============================
Timestamp: Tue Aug 02 09:05:00 WIB 2022
Hostname: app-database-staging-02
Kernel: 5.4.0-122-generic

1. Testing Internet Connectivity:
   ✗ Internet connectivity: FAILED

2. Testing DNS Resolution:
   ✗ DNS resolution: FAILED

3. Testing Package Manager:
   ✗ APT connectivity: FAILED

Network Interfaces:
  2: ens3: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500
      inet *************/24 brd ************* scope global ens3

Routing Table:
  default via *********** dev ens3 proto dhcp metric 100
  ***********/24 dev ens3 proto kernel scope link src *************

DNS Configuration:
  (No DNS servers configured)
```

### Phase 2: DNS Configuration Analysis

**DNS Resolution Investigation**:
```bash
#!/bin/bash
# dns-troubleshooting.sh

investigate_dns_issues() {
    echo "DNS Troubleshooting Analysis"
    echo "==========================="
    
    # Check systemd-resolved status
    echo "1. Systemd-Resolved Status:"
    systemctl status systemd-resolved --no-pager | head -10 | sed 's/^/   /'
    
    # Check resolvectl detailed status
    echo
    echo "2. Detailed DNS Configuration:"
    resolvectl status | sed 's/^/   /'
    
    # Check /etc/resolv.conf
    echo
    echo "3. /etc/resolv.conf Content:"
    cat /etc/resolv.conf | sed 's/^/   /'
    
    # Check netplan configuration
    echo
    echo "4. Netplan Configuration:"
    if [ -f /etc/netplan/50-cloud-init.yaml ]; then
        echo "   Found: /etc/netplan/50-cloud-init.yaml"
        cat /etc/netplan/50-cloud-init.yaml | sed 's/^/   /'
    else
        echo "   Netplan config not found"
    fi
    
    # Test manual DNS query
    echo
    echo "5. Manual DNS Query Test:"
    if dig @******* google.com >/dev/null 2>&1; then
        echo "   ✓ External DNS (*******): WORKING"
    else
        echo "   ✗ External DNS (*******): FAILED"
    fi
}

investigate_dns_issues
```

**Key Finding**: DNS servers not configured in netplan after kernel upgrade!

### Phase 3: DNS Configuration Fix

**Netplan DNS Configuration**:
```bash
#!/bin/bash
# fix-dns-configuration.sh

fix_dns_configuration() {
    echo "Fixing DNS Configuration"
    echo "======================="
    
    # Backup current netplan config
    NETPLAN_FILE="/etc/netplan/50-cloud-init.yaml"
    BACKUP_FILE="/etc/netplan/50-cloud-init.yaml.backup.$(date +%Y%m%d_%H%M%S)"
    
    echo "1. Creating backup of netplan configuration..."
    cp "$NETPLAN_FILE" "$BACKUP_FILE"
    echo "   Backup created: $BACKUP_FILE"
    
    # Add DNS configuration
    echo
    echo "2. Adding DNS configuration to netplan..."
    
    # Create updated netplan config
    cat > "$NETPLAN_FILE" << 'EOF'
network:
    version: 2
    ethernets:
        ens3:
            dhcp4: true
            nameservers:
                addresses: [************, *******]
            routes:
                - to: 0.0.0.0/0
                  via: ***********
EOF
    
    echo "   DNS servers added: ************, *******"
    
    # Validate netplan configuration
    echo
    echo "3. Validating netplan configuration..."
    if netplan try --timeout 30; then
        echo "   ✓ Netplan configuration valid"
    else
        echo "   ✗ Netplan configuration invalid, restoring backup"
        cp "$BACKUP_FILE" "$NETPLAN_FILE"
        return 1
    fi
    
    # Apply configuration
    echo
    echo "4. Applying netplan configuration..."
    echo "   WARNING: This will cause brief network interruption"
    netplan apply
    
    # Wait for network to stabilize
    sleep 5
    
    # Test DNS resolution
    echo
    echo "5. Testing DNS resolution..."
    if nslookup google.com >/dev/null 2>&1; then
        echo "   ✓ DNS resolution: WORKING"
        return 0
    else
        echo "   ✗ DNS resolution: STILL FAILED"
        return 1
    fi
}

fix_dns_configuration
```

### Phase 4: Proxy Configuration Issues

**Proxy Environment Setup**:
```bash
#!/bin/bash
# fix-proxy-configuration.sh

configure_proxy_settings() {
    echo "Configuring Proxy Settings"
    echo "=========================="
    
    # Set environment proxy variables
    echo "1. Setting environment proxy variables..."
    
    PROXY_SERVER="http://proxy.site-alpha.local:8080"
    
    # Add to current session
    export http_proxy="$PROXY_SERVER"
    export https_proxy="$PROXY_SERVER"
    export ftp_proxy="$PROXY_SERVER"
    export no_proxy="localhost,127.0.0.1,*********/16,*********/16"
    
    # Add to system-wide environment
    cat > /etc/environment << EOF
http_proxy="$PROXY_SERVER"
https_proxy="$PROXY_SERVER"
ftp_proxy="$PROXY_SERVER"
no_proxy="localhost,127.0.0.1,*********/16,*********/16"
EOF
    
    echo "   Proxy configured: $PROXY_SERVER"
    
    # Test internet connectivity with proxy
    echo
    echo "2. Testing internet connectivity with proxy..."
    if curl -s --connect-timeout 10 google.com >/dev/null 2>&1; then
        echo "   ✓ Internet connectivity with proxy: WORKING"
    else
        echo "   ✗ Internet connectivity with proxy: FAILED"
        return 1
    fi
    
    return 0
}

configure_proxy_settings
```

### Phase 5: APT Proxy Configuration Fix

**APT Proxy Issues Resolution**:
```bash
#!/bin/bash
# fix-apt-proxy-configuration.sh

fix_apt_proxy_issues() {
    echo "Fixing APT Proxy Configuration"
    echo "=============================="
    
    APT_PROXY_FILE="/etc/apt/apt.conf.d/proxy.conf"
    
    # Check current APT proxy configuration
    echo "1. Checking current APT proxy configuration..."
    if [ -f "$APT_PROXY_FILE" ]; then
        echo "   Current proxy configuration:"
        cat "$APT_PROXY_FILE" | sed 's/^/      /'
        
        # Backup current configuration
        BACKUP_FILE="$APT_PROXY_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$APT_PROXY_FILE" "$BACKUP_FILE"
        echo "   Backup created: $BACKUP_FILE"
    else
        echo "   No APT proxy configuration found"
    fi
    
    # Comment out problematic proxy settings
    echo
    echo "2. Disabling problematic proxy settings..."
    
    if [ -f "$APT_PROXY_FILE" ]; then
        # Comment out all proxy lines
        sed -i 's/^Acquire/#Acquire/g' "$APT_PROXY_FILE"
        echo "   Proxy settings commented out"
        
        echo "   Updated configuration:"
        cat "$APT_PROXY_FILE" | sed 's/^/      /'
    fi
    
    # Test APT connectivity
    echo
    echo "3. Testing APT connectivity..."
    if timeout 30 apt update >/dev/null 2>&1; then
        echo "   ✓ APT connectivity: WORKING"
        return 0
    else
        echo "   ✗ APT connectivity: STILL FAILED"
        return 1
    fi
}

fix_apt_proxy_issues
```

## Kernel Upgrade Completion

### Safe Kernel Installation:
```bash
#!/bin/bash
# complete-kernel-upgrade.sh

complete_kernel_upgrade() {
    echo "Completing Kernel Upgrade"
    echo "========================"
    
    TARGET_KERNEL="linux-image-5.4.0-122-generic"
    
    # Verify network connectivity
    echo "1. Verifying network connectivity..."
    if ! curl -s --connect-timeout 5 google.com >/dev/null 2>&1; then
        echo "   ✗ Network connectivity required for kernel upgrade"
        return 1
    fi
    echo "   ✓ Network connectivity verified"
    
    # Update package lists
    echo
    echo "2. Updating package lists..."
    if apt update; then
        echo "   ✓ Package lists updated"
    else
        echo "   ✗ Failed to update package lists"
        return 1
    fi
    
    # Install target kernel
    echo
    echo "3. Installing target kernel: $TARGET_KERNEL"
    if apt install -y "$TARGET_KERNEL"; then
        echo "   ✓ Kernel installation completed"
    else
        echo "   ✗ Kernel installation failed"
        return 1
    fi
    
    # Verify kernel installation
    echo
    echo "4. Verifying kernel installation..."
    if dpkg -l | grep -q "$TARGET_KERNEL"; then
        echo "   ✓ Kernel package installed successfully"
    else
        echo "   ✗ Kernel package not found"
        return 1
    fi
    
    # Schedule reboot
    echo
    echo "5. Kernel upgrade completed successfully"
    echo "   Current kernel: $(uname -r)"
    echo "   Installed kernel: $TARGET_KERNEL"
    echo "   Reboot required to activate new kernel"
    
    return 0
}

# Perform safe reboot
safe_reboot() {
    echo
    echo "Performing Safe Reboot"
    echo "====================="
    
    # Sync filesystems
    echo "1. Syncing filesystems..."
    sync
    
    # Log reboot
    logger "Kernel upgrade reboot initiated - $(date)"
    
    # Reboot system
    echo "2. Rebooting system..."
    echo "   System will reboot in 5 seconds..."
    sleep 5
    reboot
}

# Execute upgrade
if complete_kernel_upgrade; then
    read -p "Proceed with reboot? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        safe_reboot
    else
        echo "Reboot cancelled. Manual reboot required to activate new kernel."
    fi
else
    echo "Kernel upgrade failed. Please check errors above."
fi
```

## Post-Reboot Verification

### Comprehensive System Check:
```bash
#!/bin/bash
# post-reboot-verification.sh

verify_post_reboot() {
    echo "Post-Reboot Verification"
    echo "======================="
    echo "Verification time: $(date)"
    echo
    
    # Check kernel version
    echo "1. Kernel Version Check:"
    CURRENT_KERNEL=$(uname -r)
    echo "   Current kernel: $CURRENT_KERNEL"
    
    if [[ "$CURRENT_KERNEL" == "5.4.0-122-generic" ]]; then
        echo "   ✓ Target kernel active"
    else
        echo "   ⚠ Different kernel active (expected: 5.4.0-122-generic)"
    fi
    
    # Network connectivity verification
    echo
    echo "2. Network Connectivity Verification:"
    
    # DNS resolution
    if nslookup google.com >/dev/null 2>&1; then
        echo "   ✓ DNS resolution: WORKING"
    else
        echo "   ✗ DNS resolution: FAILED"
    fi
    
    # Internet connectivity
    if curl -s --connect-timeout 5 google.com >/dev/null 2>&1; then
        echo "   ✓ Internet connectivity: WORKING"
    else
        echo "   ✗ Internet connectivity: FAILED"
    fi
    
    # Package manager
    if timeout 10 apt update >/dev/null 2>&1; then
        echo "   ✓ APT connectivity: WORKING"
    else
        echo "   ✗ APT connectivity: FAILED"
    fi
    
    # System services
    echo
    echo "3. Critical Services Status:"
    SERVICES=("systemd-resolved" "networking" "ssh")
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active "$service" >/dev/null 2>&1; then
            echo "   ✓ $service: ACTIVE"
        else
            echo "   ✗ $service: INACTIVE"
        fi
    done
    
    # System resources
    echo
    echo "4. System Resources:"
    echo "   Memory usage: $(free -h | grep Mem | awk '{print $3"/"$2}')"
    echo "   Disk usage: $(df -h / | tail -1 | awk '{print $5}')"
    echo "   Load average: $(uptime | awk -F'load average:' '{print $2}')"
    
    echo
    echo "Verification completed!"
}

verify_post_reboot
```

## Troubleshooting Checklist

### Quick Reference Guide:
```bash
# Kernel upgrade troubleshooting checklist

# 1. Network connectivity issues
□ Check DNS configuration in netplan
□ Verify proxy environment variables
□ Test manual DNS resolution
□ Check APT proxy configuration

# 2. DNS resolution problems
□ Restart systemd-resolved service
□ Check /etc/resolv.conf content
□ Verify nameserver configuration
□ Test with external DNS servers

# 3. Proxy configuration issues
□ Check environment proxy variables
□ Verify APT proxy settings
□ Test connectivity with/without proxy
□ Check no_proxy exclusions

# 4. Package manager problems
□ Comment out problematic proxy settings
□ Clear APT cache if needed
□ Test with different repositories
□ Check repository accessibility

# 5. Post-reboot verification
□ Confirm target kernel is active
□ Verify all network services
□ Test application connectivity
□ Monitor system performance
```

## Results & Lessons Learned

### Upgrade Results:
```
Instance: app-database-staging-02
Original Kernel: 5.4.0-72-generic
Final Kernel: 5.4.0-122-generic
Upgrade Status: SUCCESS
Network Issues: RESOLVED
Downtime: ~15 minutes (including reboot)
```

### Key Lessons:
1. **Network configuration** dapat berubah setelah kernel upgrade
2. **DNS settings** perlu diverifikasi dan dikonfigurasi ulang
3. **Proxy configuration** sering menjadi bottleneck
4. **Systematic troubleshooting** essential untuk quick resolution
5. **Post-reboot verification** critical untuk ensure stability

### Prevention Strategies:
```bash
# Pre-upgrade preparation checklist
1. Backup current network configuration
2. Document current DNS and proxy settings
3. Prepare rollback procedures
4. Schedule maintenance window
5. Notify stakeholders about potential downtime
```

## Kesimpulan

Kernel upgrade troubleshooting membutuhkan **systematic approach** dan understanding tentang **network configuration dependencies**. Yang paling critical adalah:

1. **DNS configuration** sering hilang setelah kernel upgrade
2. **Proxy settings** perlu diverifikasi dan disesuaikan
3. **APT configuration** bisa conflict dengan proxy settings
4. **Step-by-step verification** essential untuk identify root cause
5. **Post-upgrade testing** important untuk ensure system stability

Yang paling valuable adalah **preparation** dan **systematic troubleshooting methodology** untuk minimize downtime dan ensure successful upgrades.

Temen-temen punya pengalaman dengan kernel upgrade issues lainnya? Atau ada troubleshooting approaches yang berbeda? Share di comments ya! 🔧

---

*Troubleshooting ini berdasarkan pengalaman real kernel upgrade di production environment. Semua IP addresses dan hostnames telah dianonymized untuk keamanan.*

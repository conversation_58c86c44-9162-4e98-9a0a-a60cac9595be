---
title: "Strategi Cleanup Resource OpenStack: Automasi Pembersihan Volume, Port, dan Snapshot"
date: "2025-01-12"
tags: ["openstack", "resource-management", "automation", "cleanup", "scripting", "cloud-operations"]
category: "Technology"
summary: "Pengalaman membangun sistem automated cleanup untuk resource OpenStack yang tidak terpakai. Dari manual audit hingga scripted cleanup untuk volume, port, floating IP, dan snapshot yang orphaned."
thumbnail: ""
author: "Febryan Ramadhan"
difficulty: "Intermediate"
keywords: ["openstack cleanup", "resource management", "automated cleanup", "cloud operations", "openstack scripting"]
draft: false
# Open Graph metadata for social media
openGraph:
  title: "Strategi Cleanup Resource OpenStack: Automasi Pembersihan Volume, Port, dan Snapshot"
  description: "Membangun sistem automated cleanup untuk resource OpenStack yang tidak terpakai dengan scripting dan automation."
  image: ""
  type: "article"
# Twitter Card metadata
twitter:
  card: "summary_large_image"
  title: "Strategi Cleanup Resource OpenStack: Automasi Pembersihan"
  description: "Automated cleanup untuk resource OpenStack yang tidak terpakai dengan scripting dan automation."
  image: ""
# Schema.org structured data
schema:
  type: "BlogPosting"
  author:
    name: "Febryan Ramadhan"
    url: "https://febryan.web.id"
    sameAs: [
      "https://twitter.com/pepryan",
      "https://instagram.com/nayrbef",
      "https://linkedin.com/in/febryanramadhan"
    ]
  publisher:
    name: "Febryan Ramadhan Portfolio"
    url: "https://febryan.web.id"
---

Sebagai cloud engineer yang mengelola OpenStack platform multi-site, salah satu task maintenance yang sering diabaikan adalah **resource cleanup**. Over time, accumulation dari orphaned volumes, unused ports, old snapshots, dan floating IPs yang tidak terpakai bisa significantly impact platform performance dan cost efficiency.

Di post ini, saya akan sharing pengalaman membangun **automated cleanup system** untuk OpenStack resources yang mencakup identification, validation, dan safe deletion dari unused resources across multiple projects dan sites.

## Problem Statement

### Resource Accumulation Issues:
- **Orphaned volumes** dari deleted instances
- **Unused ports** yang tidak ter-attach ke instance manapun  
- **Old snapshots** yang sudah tidak diperlukan
- **Floating IPs** yang tidak ter-assign
- **Obsolete images** dari testing activities

### Business Impact:
- **Storage waste** dari unused volumes dan snapshots
- **Network resource exhaustion** dari unused ports
- **IP address shortage** dari unassigned floating IPs
- **Performance degradation** dari excessive resource listing
- **Cost inefficiency** untuk unused cloud resources

## Solution Architecture

Kami design comprehensive cleanup system dengan safety-first approach:

```
┌─────────────────────────────────────────┐
│           Resource Discovery            │
│                                         │
│  ┌─────────────┐    ┌─────────────┐     │
│  │ Site Alpha  │    │ Site Beta   │     │
│  │ Resources   │    │ Resources   │     │
│  └─────────────┘    └─────────────┘     │
└─────────────────────────────────────────┘
                     │
              ┌─────────────┐
              │ Analysis &  │
              │ Validation  │
              │             │
              │ - Orphaned  │
              │ - Unused    │
              │ - Safe to   │
              │   Delete    │
              └─────────────┘
                     │
              ┌─────────────┐
              │ Cleanup     │
              │ Execution   │
              │             │
              │ - Staged    │
              │ - Verified  │
              │ - Logged    │
              └─────────────┘
```

## Implementation Scripts

### 1. Orphaned Port Detection

```bash
#!/bin/bash
# detect-orphaned-ports.sh

detect_orphaned_ports() {
    local site=$1
    echo "Detecting orphaned ports in $site..."
    
    # Get all ports
    local port_list=$(openstack port list -c ID -f value)
    
    echo "ID,Name,MAC,Fixed_IPs,Status,Security_Groups,Device_Owner,Tags,Admin_State,Project" > orphaned-ports-$site.csv
    
    while IFS= read -r port_id; do
        if [ -n "$port_id" ]; then
            # Get detailed port information
            local port_info=$(openstack port show $port_id -f json 2>/dev/null)
            
            if [ $? -eq 0 ]; then
                # Extract port details
                local device_id=$(echo $port_info | jq -r '.device_id')
                local device_owner=$(echo $port_info | jq -r '.device_owner')
                local admin_state=$(echo $port_info | jq -r '.admin_state_up')
                local name=$(echo $port_info | jq -r '.name')
                local mac_address=$(echo $port_info | jq -r '.mac_address')
                local fixed_ips=$(echo $port_info | jq -r '.fixed_ips' | jq -c .)
                local status=$(echo $port_info | jq -r '.status')
                local security_groups=$(echo $port_info | jq -r '.security_group_ids' | jq -c .)
                local tags=$(echo $port_info | jq -r '.tags')
                local project_id=$(echo $port_info | jq -r '.project_id')
                
                # Get project name
                local project_name=$(openstack project show $project_id -c name -f value 2>/dev/null || echo "unknown")
                
                # Check if port is orphaned (no device_id means not attached)
                if [[ -z "$device_id" || "$device_id" == "null" ]]; then
                    echo "$port_id,$name,$mac_address,$fixed_ips,$status,$security_groups,$device_owner,$tags,$admin_state,$project_name" >> orphaned-ports-$site.csv
                    echo "Found orphaned port: $port_id ($name) in project $project_name"
                fi
            fi
        fi
    done <<< "$port_list"
    
    echo "Orphaned ports saved to: orphaned-ports-$site.csv"
}

# Usage for both sites
detect_orphaned_ports "alpha"
detect_orphaned_ports "beta"
```

### 2. Unattached Floating IP Detection

```bash
#!/bin/bash
# detect-unattached-floating-ips.sh

detect_unattached_floating_ips() {
    local site=$1
    echo "Detecting unattached floating IPs in $site..."
    
    echo "ID,IP_Address,Status,Port_ID,Fixed_IP,Project,Created_At" > unattached-floating-ips-$site.csv
    
    # Get all floating IPs
    openstack floating ip list -f json | jq -r '.[] | select(.["Port ID"] == null or .["Port ID"] == "") | [.ID, .["Floating IP Address"], .Status, .["Port ID"], .["Fixed IP Address"], .Project, .["Created At"]] | @csv' >> unattached-floating-ips-$site.csv
    
    local count=$(tail -n +2 unattached-floating-ips-$site.csv | wc -l)
    echo "Found $count unattached floating IPs in $site"
}

# Usage
detect_unattached_floating_ips "alpha"
detect_unattached_floating_ips "beta"
```

### 3. Orphaned Volume Detection

```bash
#!/bin/bash
# detect-orphaned-volumes.sh

detect_orphaned_volumes() {
    local site=$1
    echo "Detecting orphaned volumes in $site..."
    
    echo "ID,Name,Status,Size,Type,Bootable,Attached_To,Project,Created_At" > orphaned-volumes-$site.csv
    
    # Get all volumes with status 'available' (not attached)
    local volumes=$(openstack volume list --status available -f json)
    
    echo "$volumes" | jq -r '.[] | [.ID, .Name, .Status, .Size, .Type, .Bootable, .["Attached to"], .Project, .["Created At"]] | @csv' >> orphaned-volumes-$site.csv
    
    local count=$(echo "$volumes" | jq length)
    echo "Found $count orphaned volumes in $site"
    
    # Additional check for volumes attached to deleted instances
    echo "Checking for volumes attached to non-existent instances..."
    local attached_volumes=$(openstack volume list --status in-use -f json)
    
    echo "$attached_volumes" | jq -r '.[] | select(.["Attached to"] != null)' | while read -r volume; do
        local attached_to=$(echo "$volume" | jq -r '.["Attached to"]')
        local instance_id=$(echo "$attached_to" | grep -oP 'Attached to \K[a-f0-9-]+')
        
        if [ -n "$instance_id" ]; then
            # Check if instance exists
            openstack server show "$instance_id" >/dev/null 2>&1
            if [ $? -ne 0 ]; then
                echo "Volume attached to non-existent instance: $(echo "$volume" | jq -r '.ID') -> $instance_id"
                echo "$volume" | jq -r '[.ID, .Name, .Status, .Size, .Type, .Bootable, .["Attached to"], .Project, .["Created At"]] | @csv' >> orphaned-volumes-$site.csv
            fi
        fi
    done
}

# Usage
detect_orphaned_volumes "alpha"
detect_orphaned_volumes "beta"
```

### 4. Old Snapshot Cleanup

```bash
#!/bin/bash
# detect-old-snapshots.sh

detect_old_snapshots() {
    local site=$1
    local days_old=${2:-90}  # Default 90 days
    
    echo "Detecting snapshots older than $days_old days in $site..."
    
    echo "ID,Name,Status,Size,Volume_ID,Project,Created_At,Age_Days" > old-snapshots-$site.csv
    
    # Get all snapshots
    local snapshots=$(openstack volume snapshot list -f json)
    local cutoff_date=$(date -d "$days_old days ago" +%Y-%m-%d)
    
    echo "$snapshots" | jq -r --arg cutoff "$cutoff_date" '.[] | select(.["Created At"] < $cutoff) | [.ID, .Name, .Status, .Size, .["Volume ID"], .Project, .["Created At"], (now - (.["Created At"] | strptime("%Y-%m-%d %H:%M:%S") | mktime)) / 86400 | floor] | @csv' >> old-snapshots-$site.csv
    
    local count=$(tail -n +2 old-snapshots-$site.csv | wc -l)
    echo "Found $count snapshots older than $days_old days in $site"
}

# Usage
detect_old_snapshots "alpha" 90
detect_old_snapshots "beta" 90
```

## Safe Cleanup Procedures

### 1. Staged Cleanup with Validation

```bash
#!/bin/bash
# safe-cleanup.sh

CLEANUP_LOG="/var/log/openstack-cleanup.log"
DRY_RUN=${DRY_RUN:-true}  # Set to false for actual deletion

log_action() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$CLEANUP_LOG"
}

cleanup_orphaned_ports() {
    local csv_file=$1
    
    log_action "Starting orphaned port cleanup from $csv_file"
    
    # Skip header line
    tail -n +2 "$csv_file" | while IFS=',' read -r port_id name mac fixed_ips status security_groups device_owner tags admin_state project; do
        # Remove quotes from CSV
        port_id=$(echo "$port_id" | tr -d '"')
        name=$(echo "$name" | tr -d '"')
        project=$(echo "$project" | tr -d '"')
        
        log_action "Processing port: $port_id ($name) in project $project"
        
        # Double-check port is still orphaned
        local current_device_id=$(openstack port show "$port_id" -f value -c device_id 2>/dev/null)
        
        if [ -z "$current_device_id" ] || [ "$current_device_id" = "None" ]; then
            if [ "$DRY_RUN" = "true" ]; then
                log_action "DRY RUN: Would delete orphaned port $port_id"
            else
                log_action "Deleting orphaned port: $port_id"
                openstack port delete "$port_id"
                if [ $? -eq 0 ]; then
                    log_action "Successfully deleted port: $port_id"
                else
                    log_action "ERROR: Failed to delete port: $port_id"
                fi
            fi
        else
            log_action "SKIP: Port $port_id is now attached to device: $current_device_id"
        fi
        
        sleep 1  # Rate limiting
    done
}

cleanup_unattached_floating_ips() {
    local csv_file=$1
    
    log_action "Starting unattached floating IP cleanup from $csv_file"
    
    tail -n +2 "$csv_file" | while IFS=',' read -r fip_id ip_address status port_id fixed_ip project created_at; do
        fip_id=$(echo "$fip_id" | tr -d '"')
        ip_address=$(echo "$ip_address" | tr -d '"')
        project=$(echo "$project" | tr -d '"')
        
        log_action "Processing floating IP: $fip_id ($ip_address) in project $project"
        
        # Double-check floating IP is still unattached
        local current_port_id=$(openstack floating ip show "$fip_id" -f value -c port_id 2>/dev/null)
        
        if [ -z "$current_port_id" ] || [ "$current_port_id" = "None" ]; then
            if [ "$DRY_RUN" = "true" ]; then
                log_action "DRY RUN: Would delete unattached floating IP $fip_id ($ip_address)"
            else
                log_action "Deleting unattached floating IP: $fip_id ($ip_address)"
                openstack floating ip delete "$fip_id"
                if [ $? -eq 0 ]; then
                    log_action "Successfully deleted floating IP: $fip_id"
                else
                    log_action "ERROR: Failed to delete floating IP: $fip_id"
                fi
            fi
        else
            log_action "SKIP: Floating IP $fip_id is now attached to port: $current_port_id"
        fi
        
        sleep 1
    done
}

# Usage examples
# DRY_RUN=true ./safe-cleanup.sh  # Test run
# DRY_RUN=false ./safe-cleanup.sh # Actual cleanup

cleanup_orphaned_ports "orphaned-ports-alpha.csv"
cleanup_unattached_floating_ips "unattached-floating-ips-alpha.csv"
```

### 2. Cleanup Validation and Reporting

```bash
#!/bin/bash
# cleanup-report.sh

generate_cleanup_report() {
    local site=$1
    local report_file="cleanup-report-$site-$(date +%Y%m%d).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>OpenStack Cleanup Report - $site</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .summary { background-color: #e7f3ff; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>OpenStack Resource Cleanup Report - $site</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p>Report generated on: $(date)</p>
        <p>Site: $site</p>
    </div>
EOF

    # Add orphaned ports summary
    if [ -f "orphaned-ports-$site.csv" ]; then
        local port_count=$(tail -n +2 "orphaned-ports-$site.csv" | wc -l)
        echo "<h2>Orphaned Ports ($port_count found)</h2>" >> "$report_file"
        echo "<table>" >> "$report_file"
        echo "<tr><th>Port ID</th><th>Name</th><th>Project</th><th>Status</th></tr>" >> "$report_file"
        
        tail -n +2 "orphaned-ports-$site.csv" | head -20 | while IFS=',' read -r port_id name mac fixed_ips status security_groups device_owner tags admin_state project; do
            echo "<tr><td>$(echo "$port_id" | tr -d '"')</td><td>$(echo "$name" | tr -d '"')</td><td>$(echo "$project" | tr -d '"')</td><td>$(echo "$status" | tr -d '"')</td></tr>" >> "$report_file"
        done
        
        echo "</table>" >> "$report_file"
        if [ $port_count -gt 20 ]; then
            echo "<p><em>Showing first 20 of $port_count orphaned ports. See CSV file for complete list.</em></p>" >> "$report_file"
        fi
    fi
    
    # Add floating IPs summary
    if [ -f "unattached-floating-ips-$site.csv" ]; then
        local fip_count=$(tail -n +2 "unattached-floating-ips-$site.csv" | wc -l)
        echo "<h2>Unattached Floating IPs ($fip_count found)</h2>" >> "$report_file"
        echo "<table>" >> "$report_file"
        echo "<tr><th>IP Address</th><th>Project</th><th>Created At</th></tr>" >> "$report_file"
        
        tail -n +2 "unattached-floating-ips-$site.csv" | while IFS=',' read -r fip_id ip_address status port_id fixed_ip project created_at; do
            echo "<tr><td>$(echo "$ip_address" | tr -d '"')</td><td>$(echo "$project" | tr -d '"')</td><td>$(echo "$created_at" | tr -d '"')</td></tr>" >> "$report_file"
        done
        
        echo "</table>" >> "$report_file"
    fi
    
    echo "</body></html>" >> "$report_file"
    echo "Cleanup report generated: $report_file"
}

# Generate reports for both sites
generate_cleanup_report "alpha"
generate_cleanup_report "beta"
```

## Automation and Scheduling

### Weekly Cleanup Cron Job

```bash
#!/bin/bash
# weekly-cleanup.sh

SCRIPT_DIR="/opt/openstack-cleanup"
LOG_DIR="/var/log/openstack-cleanup"
DATE=$(date +%Y%m%d)

# Create log directory
mkdir -p "$LOG_DIR"

# Source OpenStack credentials
source /home/<USER>/admin-openrc

cd "$SCRIPT_DIR"

# Run detection scripts
echo "Starting weekly OpenStack cleanup - $DATE"

# Site Alpha
echo "Processing Site Alpha..."
./detect-orphaned-ports.sh alpha
./detect-unattached-floating-ips.sh alpha
./detect-orphaned-volumes.sh alpha
./detect-old-snapshots.sh alpha 90

# Site Beta  
echo "Processing Site Beta..."
./detect-orphaned-ports.sh beta
./detect-unattached-floating-ips.sh beta
./detect-orphaned-volumes.sh beta
./detect-old-snapshots.sh beta 90

# Generate reports
./cleanup-report.sh

# Archive results
tar -czf "$LOG_DIR/cleanup-results-$DATE.tar.gz" *.csv *.html

# Cleanup old files
find "$LOG_DIR" -name "cleanup-results-*.tar.gz" -mtime +30 -delete

echo "Weekly cleanup detection completed - $DATE"
```

**Cron Configuration**:
```bash
# Run every Sunday at 2 AM
0 2 * * 0 /opt/openstack-cleanup/weekly-cleanup.sh >> /var/log/openstack-cleanup/weekly-cleanup.log 2>&1
```

## Results & Best Practices

### Cleanup Results:
- **Orphaned ports**: 150+ ports cleaned up across both sites
- **Unattached floating IPs**: 25+ IPs reclaimed for reuse
- **Old snapshots**: 200+ snapshots older than 90 days removed
- **Storage reclaimed**: 2TB+ from unused volumes dan snapshots

### Safety Best Practices:
1. **Always run dry-run first** untuk validate detection logic
2. **Double-check resource status** before deletion
3. **Coordinate with teams** untuk avoid deleting active resources
4. **Maintain detailed logs** untuk audit trail
5. **Implement rate limiting** untuk avoid API overload

### Lessons Learned:
- **Manual validation** essential untuk critical resources
- **Staged cleanup** safer than bulk operations
- **Regular scheduling** prevents resource accumulation
- **Comprehensive reporting** helps track cleanup effectiveness

## Kesimpulan

Implementasi automated cleanup system ini significantly improved platform efficiency dan resource utilization. Key benefits:

1. **Proactive resource management** prevents accumulation
2. **Cost optimization** through unused resource elimination  
3. **Performance improvement** dengan cleaner resource listings
4. **Operational efficiency** dengan automated detection dan reporting

Yang paling valuable adalah **shift dari reactive ke proactive** resource management, enabling better platform hygiene dan cost control.

Temen-temen punya pengalaman dengan OpenStack resource cleanup lainnya? Atau ada automation approaches yang berbeda? Share di comments ya! 🧹

---

*Artikel ini berdasarkan pengalaman cleanup resource untuk OpenStack platform multi-site dengan 500+ projects. Semua identifiers telah dianonymized untuk keamanan.*
